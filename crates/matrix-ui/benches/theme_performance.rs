//! Benchmark specifici per le performance dei temi
//!
//! Questo benchmark si concentra sulle operazioni sui temi che sono
//! critiche per le performance dell'UI.

use divan::Bencher;
use matrix_ui::theme::{ThemeManager, Theme, ThemeColors};
use floem::peniko::Color;
use std::collections::HashMap;

fn main() {
    divan::main();
}

/// Crea un tema di test per i benchmark
fn create_test_theme() -> Theme {
    let colors = ThemeColors {
        background: Color::rgb8(0x21, 0x21, 0x21),
        background_secondary: Color::rgb8(0x2D, 0x2D, 0x2D),
        background_tertiary: Color::rgb8(0x3D, 0x3D, 0x3D),
        text: Color::rgb8(0xE0, 0xE0, 0xE0),
        text_secondary: Color::rgb8(0xA0, 0xA0, 0xA0),
        text_tertiary: Color::rgb8(0x80, 0x80, 0x80),
        accent: Color::rgb8(0x00, 0x7A, 0xCC),
        accent_secondary: Color::rgb8(0x00, 0x5A, 0x9C),
        border: Color::rgb8(0x40, 0x40, 0x40),
        error: Color::rgb8(0xF4, 0x43, 0x36),
        warning: Color::rgb8(0xFF, 0x98, 0x00),
        success: Color::rgb8(0x4C, 0xAF, 0x50),
        info: Color::rgb8(0x21, 0x96, 0xF3),
        primary: Color::rgb8(0x00, 0x7A, 0xCC),
        surface: Color::rgb8(0x25, 0x25, 0x25),
        transparent: Color::rgba8(0, 0, 0, 0),
        hover: Color::rgb8(0x26, 0x4F, 0x78),
        active: Color::rgb8(0xFF, 0xFF, 0x00),
        danger: Color::rgb8(0xF4, 0x43, 0x36),
        danger_active: Color::rgb8(0xD3, 0x2F, 0x2F),
    };
    
    Theme {
        name: "Benchmark Theme".to_string(),
        description: "Theme for benchmarking".to_string(),
        author: "MATRIX IDE".to_string(),
        version: "1.0.0".to_string(),
        colors,
        spacing: Default::default(),
        font_sizes: Default::default(),
        icons: Default::default(),
        borders: Default::default(),
        is_dark: true,
    }
}

/// Benchmark per la creazione di colori
#[divan::bench]
fn bench_color_creation(bencher: Bencher) {
    bencher.bench(|| {
        let _colors = ThemeColors {
            background: Color::rgb8(0x21, 0x21, 0x21),
            background_secondary: Color::rgb8(0x2D, 0x2D, 0x2D),
            background_tertiary: Color::rgb8(0x3D, 0x3D, 0x3D),
            text: Color::rgb8(0xE0, 0xE0, 0xE0),
            text_secondary: Color::rgb8(0xA0, 0xA0, 0xA0),
            text_tertiary: Color::rgb8(0x80, 0x80, 0x80),
            accent: Color::rgb8(0x00, 0x7A, 0xCC),
            accent_secondary: Color::rgb8(0x00, 0x5A, 0x9C),
            border: Color::rgb8(0x40, 0x40, 0x40),
            error: Color::rgb8(0xF4, 0x43, 0x36),
            warning: Color::rgb8(0xFF, 0x98, 0x00),
            success: Color::rgb8(0x4C, 0xAF, 0x50),
            info: Color::rgb8(0x21, 0x96, 0xF3),
            primary: Color::rgb8(0x00, 0x7A, 0xCC),
            surface: Color::rgb8(0x25, 0x25, 0x25),
            transparent: Color::rgba8(0, 0, 0, 0),
            hover: Color::rgb8(0x26, 0x4F, 0x78),
            active: Color::rgb8(0xFF, 0xFF, 0x00),
            danger: Color::rgb8(0xF4, 0x43, 0x36),
            danger_active: Color::rgb8(0xD3, 0x2F, 0x2F),
        };
    });
}

/// Benchmark per la clonazione di temi
#[divan::bench]
fn bench_theme_clone(bencher: Bencher) {
    let theme = create_test_theme();
    
    bencher.bench(|| {
        let _cloned = theme.clone();
    });
}

/// Benchmark per l'accesso ai colori del tema
#[divan::bench]
fn bench_theme_color_access(bencher: Bencher) {
    let theme = create_test_theme();
    
    bencher.bench(|| {
        let _bg = theme.colors.background;
        let _text = theme.colors.text;
        let _accent = theme.colors.accent;
        let _error = theme.colors.error;
    });
}

/// Benchmark per la modifica dei colori del tema
#[divan::bench]
fn bench_theme_color_modification(bencher: Bencher) {
    bencher.bench(|| {
        let mut theme = create_test_theme();
        theme.colors.background = Color::rgb8(0x30, 0x30, 0x30);
        theme.colors.text = Color::rgb8(0xF0, 0xF0, 0xF0);
    });
}

/// Benchmark per la conversione di colori
#[divan::bench]
fn bench_color_conversion(bencher: Bencher) {
    let color = Color::rgb8(0x21, 0x21, 0x21);
    
    bencher.bench(|| {
        let _rgba = (color.r, color.g, color.b, color.a); // Extract RGBA components
        let _hex = format!("#{:02x}{:02x}{:02x}", _rgba.0, _rgba.1, _rgba.2);
    });
}

/// Benchmark per la creazione di temi multipli
#[divan::bench]
fn bench_multiple_themes_creation(bencher: Bencher) {
    bencher.bench(|| {
        let mut themes = Vec::new();
        for i in 0..10 {
            let mut theme = create_test_theme();
            theme.name = format!("Theme {}", i);
            themes.push(theme);
        }
    });
}

/// Benchmark per la ricerca di temi
#[divan::bench]
fn bench_theme_search(bencher: Bencher) {
    let mut themes = HashMap::new();
    for i in 0..100 {
        let mut theme = create_test_theme();
        theme.name = format!("Theme {}", i);
        themes.insert(theme.name.clone(), theme);
    }
    
    bencher.bench(|| {
        let _theme = themes.get("Theme 50");
    });
}

/// Benchmark per l'interpolazione di colori
#[divan::bench]
fn bench_color_interpolation(bencher: Bencher) {
    let color1 = Color::rgb8(0x00, 0x00, 0x00);
    let color2 = Color::rgb8(0xFF, 0xFF, 0xFF);
    
    bencher.bench(|| {
        // Simula interpolazione lineare
        let t = 0.5;
        let rgba1 = (color1.r, color1.g, color1.b, color1.a);
        let rgba2 = (color2.r, color2.g, color2.b, color2.a);
        
        let _interpolated = Color::rgba8(
            (rgba1.0 as f32 * (1.0 - t) + rgba2.0 as f32 * t) as u8,
            (rgba1.1 as f32 * (1.0 - t) + rgba2.1 as f32 * t) as u8,
            (rgba1.2 as f32 * (1.0 - t) + rgba2.2 as f32 * t) as u8,
            (rgba1.3 as f32 * (1.0 - t) + rgba2.3 as f32 * t) as u8,
        );
    });
}

/// Benchmark per la validazione di temi
#[divan::bench]
fn bench_theme_validation(bencher: Bencher) {
    let theme = create_test_theme();
    
    bencher.bench(|| {
        // Simula validazione del tema
        let _valid = !theme.name.is_empty() 
            && theme.font_sizes.md > 0.0 
            && theme.spacing.md > 0.0;
    });
}

/// Benchmark per la conversione tema -> JSON
#[divan::bench]
fn bench_theme_to_json(bencher: Bencher) {
    let theme = create_test_theme();
    
    bencher.bench(|| {
        let _json = serde_json::to_string(&theme).unwrap();
    });
}

/// Benchmark per la conversione JSON -> tema
#[divan::bench]
fn bench_json_to_theme(bencher: Bencher) {
    let theme = create_test_theme();
    let json = serde_json::to_string(&theme).unwrap();
    
    bencher.bench(|| {
        let _theme: Theme = serde_json::from_str(&json).unwrap();
    });
}

/// Benchmark per la compressione di temi
#[divan::bench]
fn bench_theme_compression(bencher: Bencher) {
    let theme = create_test_theme();
    let json = serde_json::to_string(&theme).unwrap();
    
    bencher.bench(|| {
        // Simula compressione (usando lunghezza come proxy)
        let _compressed_size = json.len() / 2;
    });
}
