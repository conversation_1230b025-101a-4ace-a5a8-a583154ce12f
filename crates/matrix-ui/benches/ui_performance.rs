//! Benchmark per le performance dell'UI
//!
//! Questo benchmark misura le performance dei componenti UI principali
//! per identificare bottlenecks e ottimizzazioni.

use divan::Bencher;
use std::sync::Arc;
use matrix_ui::theme::ThemeManager;
use matrix_ui::components::dag_viewer::DagViewer;
use matrix_core::Engine as CoreEngine;

fn main() {
    divan::main();
}

/// Benchmark per la creazione del ThemeManager
#[divan::bench]
fn bench_theme_manager_creation() -> Result<(), Box<dyn std::error::Error>> {
    let _theme_manager = ThemeManager::new()?;
    Ok(())
}

/// Benchmark per il caricamento di temi
#[divan::bench]
fn bench_theme_loading(bencher: Bencher) {
    let theme_manager = ThemeManager::new().unwrap();
    
    bencher.bench(|| {
        let _theme = theme_manager.get_active_theme();
    });
}

/// Benchmark per la creazione di componenti DAG
#[divan::bench]
async fn bench_dag_viewer_creation() -> Result<(), Box<dyn std::error::Error>> {
    let core = Arc::new(CoreEngine::new().await?);
    let theme_manager = Arc::new(ThemeManager::new()?);
    
    let _dag_viewer = DagViewer::new(core, theme_manager)?;
    Ok(())
}

/// Benchmark per l'aggiornamento del tema
#[divan::bench]
fn bench_theme_switching(bencher: Bencher) {
    let theme_manager = ThemeManager::new().unwrap();
    
    bencher.bench(|| {
        // Simula il cambio di tema
        let _result = theme_manager.get_active_theme();
    });
}

/// Benchmark per la serializzazione dei temi
#[divan::bench]
fn bench_theme_serialization(bencher: Bencher) {
    use matrix_ui::theme::{Theme, ThemeColors};
    use floem::style::Color;
    
    let colors = ThemeColors {
        background: Color::rgb8(0x21, 0x21, 0x21),
        background_secondary: Color::rgb8(0x2D, 0x2D, 0x2D),
        background_tertiary: Color::rgb8(0x3D, 0x3D, 0x3D),
        text: Color::rgb8(0xE0, 0xE0, 0xE0),
        text_secondary: Color::rgb8(0xA0, 0xA0, 0xA0),
        text_tertiary: Color::rgb8(0x80, 0x80, 0x80),
        accent: Color::rgb8(0x00, 0x7A, 0xCC),
        accent_secondary: Color::rgb8(0x00, 0x5A, 0x9C),
        border: Color::rgb8(0x40, 0x40, 0x40),
        error: Color::rgb8(0xF4, 0x43, 0x36),
        warning: Color::rgb8(0xFF, 0x98, 0x00),
        success: Color::rgb8(0x4C, 0xAF, 0x50),
        info: Color::rgb8(0x21, 0x96, 0xF3),
        selection: Color::rgb8(0x26, 0x4F, 0x78),
        highlight: Color::rgb8(0xFF, 0xFF, 0x00),
    };
    
    let theme = Theme {
        name: "Test Theme".to_string(),
        colors,
        font_sizes: matrix_ui::theme::FontSizes::default(),
        spacing: matrix_ui::theme::Spacing::default(),
        border_radius: matrix_ui::theme::BorderRadius::default(),
    };
    
    bencher.bench(|| {
        let _json = serde_json::to_string(&theme);
    });
}

/// Benchmark per la deserializzazione dei temi
#[divan::bench]
fn bench_theme_deserialization(bencher: Bencher) {
    let theme_json = r#"{
        "name": "Test Theme",
        "colors": {
            "background": [33, 33, 33, 255],
            "background_secondary": [45, 45, 45, 255],
            "background_tertiary": [61, 61, 61, 255],
            "text": [224, 224, 224, 255],
            "text_secondary": [160, 160, 160, 255],
            "text_tertiary": [128, 128, 128, 255],
            "accent": [0, 122, 204, 255],
            "accent_secondary": [0, 90, 156, 255],
            "border": [64, 64, 64, 255],
            "error": [244, 67, 54, 255],
            "warning": [255, 152, 0, 255],
            "success": [76, 175, 80, 255],
            "info": [33, 150, 243, 255],
            "selection": [38, 79, 120, 255],
            "highlight": [255, 255, 0, 255]
        },
        "font_sizes": {
            "xs": 10.0,
            "sm": 12.0,
            "md": 14.0,
            "lg": 16.0,
            "xl": 18.0,
            "xxl": 24.0
        },
        "spacing": {
            "xs": 4.0,
            "sm": 8.0,
            "md": 16.0,
            "lg": 24.0,
            "xl": 32.0,
            "xxl": 48.0
        },
        "border_radius": {
            "sm": 2.0,
            "md": 4.0,
            "lg": 8.0,
            "xl": 12.0
        }
    }"#;
    
    bencher.bench(|| {
        let _theme: Result<matrix_ui::theme::Theme, _> = serde_json::from_str(theme_json);
    });
}

/// Benchmark per la creazione di layout complessi
#[divan::bench]
fn bench_layout_creation(bencher: Bencher) {
    use matrix_ui::layout::LayoutManager;
    
    bencher.bench(|| {
        let _layout = LayoutManager::new();
    });
}

/// Benchmark per l'aggiornamento di layout
#[divan::bench]
fn bench_layout_update(bencher: Bencher) {
    use matrix_ui::layout::LayoutManager;
    
    let mut layout = LayoutManager::new();
    
    bencher.bench(|| {
        layout.update_layout();
    });
}
