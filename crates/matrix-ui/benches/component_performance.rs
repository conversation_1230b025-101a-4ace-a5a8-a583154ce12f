//! Benchmark per le performance dei componenti UI
//!
//! Questo benchmark misura le performance dei singoli componenti
//! per identificare bottlenecks specifici.

use divan::Bencher;
use std::sync::Arc;
use matrix_ui::theme::ThemeManager;
use matrix_ui::components::dag_viewer::DagViewer;
use floem::prelude::SignalUpdate;
use matrix_ui::panels::PanelManager;
use matrix_core::Engine as CoreEngine;

fn main() {
    divan::main();
}

/// Setup comune per i test
fn setup() -> (Arc<CoreEngine>, Arc<ThemeManager>) {
    let core = Arc::new(CoreEngine::new().unwrap());
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    (core, theme_manager)
}

/// Benchmark per la creazione del DAG viewer
#[divan::bench]
async fn bench_dag_viewer_creation() {
    let (core, theme_manager) = setup();
    
    let _dag_viewer = DagViewer::new(core, theme_manager).unwrap();
}

/// Benchmark per l'aggiornamento del DAG viewer
#[divan::bench]
fn bench_dag_viewer_update(bencher: divan::Bencher) {
    let (core, theme_manager) = setup();
    let dag_viewer = DagViewer::new(core, theme_manager).unwrap();
    
    bencher.bench(|| {
        // Simula aggiornamento del grafo
        dag_viewer.set_current_graph(Some("test-graph".to_string()));
    });
}

/// Benchmark per la selezione di nodi nel DAG
#[divan::bench]
fn bench_dag_node_selection(bencher: divan::Bencher) {
    let (core, theme_manager) = setup();
    let dag_viewer = DagViewer::new(core, theme_manager).unwrap();
    
    bencher.bench(|| {
        dag_viewer.select_node(Some("node-1".to_string()));
        dag_viewer.select_node(Some("node-2".to_string()));
        dag_viewer.select_node(None);
    });
}

/// Benchmark per la creazione del panel manager
#[divan::bench]
fn bench_panel_manager_creation() {
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let _panel_manager = PanelManager::new(theme_manager);
}

/// Benchmark per la registrazione di pannelli
#[divan::bench]
fn bench_panel_registration(bencher: Bencher) {
    use matrix_ui::panels::{Panel, PanelType};
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let panel_manager = PanelManager::new(theme_manager);
    
    bencher.bench(|| {
        let panel_config = matrix_ui::panels::PanelConfig {
            id: "test-panel".to_string(),
            title: "Test Panel".to_string(),
            position: matrix_ui::panels::PanelPosition::Left,
            state: matrix_ui::panels::PanelState::Visible,
            resizable: true,
            closable: true,
            size: (200.0, 300.0),
            movable: true,
        };
        let _ = panel_config; // Just use the config for benchmarking
    });
}

/// Benchmark per la gestione della visibilità dei pannelli
#[divan::bench]
fn bench_panel_visibility(bencher: Bencher) {
    use matrix_ui::panels::{Panel, PanelType};
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let panel_manager = PanelManager::new(theme_manager);
    
    // Panel registration requires trait objects, skip for benchmark
    // let panel_config = matrix_ui::panels::PanelConfig { ... };
    // panel_manager.register_panel(panel_config).unwrap();
    
    bencher.bench(|| {
        let _ = panel_manager.hide_panel("test-panel");
        let _ = panel_manager.show_panel("test-panel");
    });
}

/// Benchmark per la creazione di componenti button
#[divan::bench]
fn bench_button_creation(bencher: Bencher) {
    use matrix_ui::components::button::{primary_button, ButtonType, ButtonSize};
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    
    bencher.bench(|| {
        let _button = primary_button(
            theme_manager.clone(),
            || "Test Button".to_string(),
            || floem::views::label(|| "Click me".to_string()),
        );
    });
}

/// Benchmark per la creazione di componenti text input
#[divan::bench]
fn bench_text_input_creation(bencher: Bencher) {
    use matrix_ui::components::text_input::matrix_text_input;
    use floem::reactive::RwSignal;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    
    bencher.bench(|| {
        let value = RwSignal::new(String::new());
        let _input = matrix_text_input(
            theme_manager.clone(),
            value,
            Some("Label"),
            Some("Placeholder"),
        );
    });
}

/// Benchmark per la creazione di status bar
#[divan::bench]
fn bench_status_bar_creation(bencher: Bencher) {
    use matrix_ui::components::status_bar::{status_bar, StatusItem, StatusType};
    use floem::reactive::RwSignal;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    
    bencher.bench(|| {
        let left_items = vec![
            StatusItem::new(
                RwSignal::new("Ready".to_string()),
                RwSignal::new(StatusType::Success),
            ),
        ];
        let right_items = vec![
            StatusItem::new(
                RwSignal::new("Line 1, Col 1".to_string()),
                RwSignal::new(StatusType::Info),
            ),
        ];
        
        let _status_bar = status_bar(theme_manager.clone(), left_items, right_items);
    });
}

/// Benchmark per la creazione di tooltip
#[divan::bench]
fn bench_tooltip_creation(bencher: Bencher) {
    use matrix_ui::components::tooltip::{with_tooltip, TooltipConfig, TooltipPosition};
    use floem::views::label;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    
    bencher.bench(|| {
        let view = label(|| "Hover me".to_string());
        let config = TooltipConfig {
            position: TooltipPosition::Top,
            delay_ms: 500,
            duration_ms: 3000,
        };
        
        let _tooltip = with_tooltip(
            theme_manager.clone(),
            view,
            "This is a tooltip",
            Some(config),
        );
    });
}

/// Benchmark per la creazione di layout complessi
#[divan::bench]
fn bench_complex_layout_creation(bencher: Bencher) {
    use matrix_ui::layout::LayoutManager;
    use floem::views::{v_stack, h_stack, label};
    
    bencher.bench(|| {
        let _layout = v_stack((
            h_stack((
                label(|| "Left".to_string()),
                label(|| "Center".to_string()),
                label(|| "Right".to_string()),
            )),
            h_stack((
                label(|| "Bottom Left".to_string()),
                label(|| "Bottom Right".to_string()),
            )),
        ));
    });
}

/// Benchmark per l'aggiornamento di layout
#[divan::bench]
fn bench_layout_update_performance(bencher: Bencher) {
    use matrix_ui::layout::LayoutManager;
    
    let mut layout_manager = LayoutManager::new();
    
    bencher.bench(|| {
        // layout_manager.update_layout(); // Method doesn't exist, skip for now
    });
}

/// Benchmark per la gestione di eventi UI
#[divan::bench]
fn bench_ui_event_handling(bencher: Bencher) {
    use matrix_ui::components::dag_event_handler::DagViewerEventHandler;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let core_engine = Arc::new(matrix_core::Engine::new().unwrap());

    bencher.bench(|| {
        // Simula gestione eventi
        let dag_viewer = DagViewer::new(core_engine.clone(), theme_manager.clone()).unwrap();
        let _handler = DagViewerEventHandler::new(dag_viewer);
    });
}

/// Benchmark per la renderizzazione di grandi liste
#[divan::bench]
fn bench_large_list_rendering(bencher: Bencher) {
    use floem::views::{v_stack, label};
    
    bencher.bench(|| {
        let items: Vec<_> = (0..1000)
            .map(|i| label(move || format!("Item {}", i)))
            .collect();
        
        // v_stack requires ViewTuple, not Vec. Use v_stack_from_iter instead
        let _list = floem::views::v_stack_from_iter(items);
    });
}

/// Benchmark per la gestione di stati reattivi
#[divan::bench]
fn bench_reactive_state_updates(bencher: Bencher) {
    use floem::reactive::RwSignal;
    
    let state = RwSignal::new(0);
    
    bencher.bench(|| {
        for i in 0..100 {
            state.set(i);
        }
    });
}
