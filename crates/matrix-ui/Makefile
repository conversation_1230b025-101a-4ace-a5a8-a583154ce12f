# MATRIX IDE - Makefile
# 
# Questo Makefile fornisce comandi semplificati per lo sviluppo,
# testing e deployment di MATRIX IDE.

.PHONY: help build test clean install dev docs bench coverage security quality all

# Configurazione
CARGO := cargo
PROJECT_NAME := matrix-ui
REPORTS_DIR := target/test-reports
COVERAGE_DIR := target/coverage

# Colori per output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m

# Target di default
help: ## Mostra questo messaggio di aiuto
	@echo "MATRIX IDE - Makefile Commands"
	@echo "=============================="
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

# Build targets
build: ## Compila il progetto
	@echo "$(GREEN)Building $(PROJECT_NAME)...$(NC)"
	$(CARGO) build

build-release: ## Compila il progetto in modalità release
	@echo "$(GREEN)Building $(PROJECT_NAME) in release mode...$(NC)"
	$(CARGO) build --release

clean: ## Pulisce i file di build
	@echo "$(YELLOW)Cleaning build artifacts...$(NC)"
	$(CARGO) clean
	rm -rf $(REPORTS_DIR) $(COVERAGE_DIR)

# Development targets
dev: ## Avvia il progetto in modalità development
	@echo "$(GREEN)Starting $(PROJECT_NAME) in development mode...$(NC)"
	$(CARGO) run

watch: ## Avvia il progetto con auto-reload
	@echo "$(GREEN)Starting $(PROJECT_NAME) with auto-reload...$(NC)"
	$(CARGO) install cargo-watch
	cargo watch -x run

check: ## Esegue controlli rapidi (fmt, clippy, build)
	@echo "$(GREEN)Running quick checks...$(NC)"
	$(CARGO) fmt --all -- --check
	$(CARGO) clippy --all-targets --all-features -- -D warnings
	$(CARGO) check

fix: ## Corregge automaticamente problemi di formattazione e clippy
	@echo "$(GREEN)Auto-fixing issues...$(NC)"
	$(CARGO) fmt --all
	$(CARGO) clippy --all-targets --all-features --fix --allow-dirty

# Test targets
test: ## Esegue tutti i test
	@echo "$(GREEN)Running all tests...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) test --all --verbose 2>&1 | tee $(REPORTS_DIR)/all-tests.log

test-unit: ## Esegue solo i test unitari
	@echo "$(GREEN)Running unit tests...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) test --lib --verbose 2>&1 | tee $(REPORTS_DIR)/unit-tests.log

test-integration: ## Esegue solo i test di integrazione
	@echo "$(GREEN)Running integration tests...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) test --test integration_test --verbose 2>&1 | tee $(REPORTS_DIR)/integration-tests.log

test-doc: ## Esegue i test della documentazione
	@echo "$(GREEN)Running documentation tests...$(NC)"
	$(CARGO) test --doc

test-watch: ## Esegue i test con auto-reload
	@echo "$(GREEN)Running tests with auto-reload...$(NC)"
	$(CARGO) install cargo-watch
	cargo watch -x test

# Performance targets
bench: ## Esegue i benchmark di performance
	@echo "$(GREEN)Running performance benchmarks...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) bench --bench ui_performance 2>&1 | tee $(REPORTS_DIR)/ui-benchmarks.log
	$(CARGO) bench --bench theme_performance 2>&1 | tee $(REPORTS_DIR)/theme-benchmarks.log
	$(CARGO) bench --bench component_performance 2>&1 | tee $(REPORTS_DIR)/component-benchmarks.log

bench-ui: ## Esegue solo i benchmark UI
	@echo "$(GREEN)Running UI benchmarks...$(NC)"
	$(CARGO) bench --bench ui_performance

bench-theme: ## Esegue solo i benchmark dei temi
	@echo "$(GREEN)Running theme benchmarks...$(NC)"
	$(CARGO) bench --bench theme_performance

bench-components: ## Esegue solo i benchmark dei componenti
	@echo "$(GREEN)Running component benchmarks...$(NC)"
	$(CARGO) bench --bench component_performance

# Coverage targets
coverage: ## Genera report di code coverage
	@echo "$(GREEN)Generating code coverage report...$(NC)"
	mkdir -p $(COVERAGE_DIR)
	$(CARGO) install cargo-tarpaulin
	$(CARGO) tarpaulin --out Html --output-dir $(COVERAGE_DIR) --timeout 300
	@echo "$(GREEN)Coverage report generated in $(COVERAGE_DIR)/tarpaulin-report.html$(NC)"

coverage-open: coverage ## Genera e apre il report di coverage
	@echo "$(GREEN)Opening coverage report...$(NC)"
	open $(COVERAGE_DIR)/tarpaulin-report.html || xdg-open $(COVERAGE_DIR)/tarpaulin-report.html

# Quality targets
quality: ## Esegue tutti i controlli di qualità
	@echo "$(GREEN)Running quality checks...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) fmt --all -- --check 2>&1 | tee $(REPORTS_DIR)/fmt.log
	$(CARGO) clippy --all-targets --all-features -- -D warnings 2>&1 | tee $(REPORTS_DIR)/clippy.log

security: ## Esegue audit di sicurezza
	@echo "$(GREEN)Running security audit...$(NC)"
	mkdir -p $(REPORTS_DIR)
	$(CARGO) install cargo-audit
	$(CARGO) audit 2>&1 | tee $(REPORTS_DIR)/security-audit.log

deps-check: ## Controlla le dipendenze
	@echo "$(GREEN)Checking dependencies...$(NC)"
	$(CARGO) install cargo-machete
	$(CARGO) machete

# Documentation targets
docs: ## Genera la documentazione
	@echo "$(GREEN)Generating documentation...$(NC)"
	$(CARGO) doc --all --no-deps

docs-open: docs ## Genera e apre la documentazione
	@echo "$(GREEN)Opening documentation...$(NC)"
	$(CARGO) doc --all --no-deps --open

# Installation targets
install: ## Installa il progetto
	@echo "$(GREEN)Installing $(PROJECT_NAME)...$(NC)"
	$(CARGO) install --path .

install-dev-tools: ## Installa strumenti di sviluppo
	@echo "$(GREEN)Installing development tools...$(NC)"
	$(CARGO) install cargo-watch
	$(CARGO) install cargo-tarpaulin
	$(CARGO) install cargo-audit
	$(CARGO) install cargo-machete
	$(CARGO) install cargo-complexity

# Automation targets
auto-test: ## Esegue lo script di automazione test
	@echo "$(GREEN)Running automated test suite...$(NC)"
	./scripts/test-automation.sh all

auto-test-unit: ## Esegue solo i test unitari automatizzati
	@echo "$(GREEN)Running automated unit tests...$(NC)"
	./scripts/test-automation.sh unit

auto-test-integration: ## Esegue solo i test di integrazione automatizzati
	@echo "$(GREEN)Running automated integration tests...$(NC)"
	./scripts/test-automation.sh integration

auto-test-performance: ## Esegue solo i test di performance automatizzati
	@echo "$(GREEN)Running automated performance tests...$(NC)"
	./scripts/test-automation.sh performance

# CI/CD simulation
ci-local: ## Simula la pipeline CI/CD localmente
	@echo "$(GREEN)Running local CI/CD simulation...$(NC)"
	make check
	make test
	make bench
	make coverage
	make security
	make quality
	@echo "$(GREEN)✅ Local CI/CD simulation completed successfully!$(NC)"

# Comprehensive targets
all: clean build test bench coverage quality security docs ## Esegue tutto: build, test, bench, coverage, quality, security, docs
	@echo "$(GREEN)✅ All tasks completed successfully!$(NC)"

pre-commit: ## Esegue controlli pre-commit
	@echo "$(GREEN)Running pre-commit checks...$(NC)"
	make fix
	make test-unit
	make quality
	@echo "$(GREEN)✅ Pre-commit checks completed!$(NC)"

pre-push: ## Esegue controlli pre-push
	@echo "$(GREEN)Running pre-push checks...$(NC)"
	make check
	make test
	make security
	@echo "$(GREEN)✅ Pre-push checks completed!$(NC)"

# Status and info
status: ## Mostra lo stato del progetto
	@echo "$(GREEN)Project Status:$(NC)"
	@echo "Project: $(PROJECT_NAME)"
	@echo "Rust version: $(shell rustc --version)"
	@echo "Cargo version: $(shell cargo --version)"
	@echo "Last build: $(shell ls -la target/debug/$(PROJECT_NAME) 2>/dev/null | awk '{print $$6, $$7, $$8}' || echo 'Never built')"
	@echo "Test reports: $(shell ls -la $(REPORTS_DIR) 2>/dev/null | wc -l || echo '0') files"
	@echo "Coverage reports: $(shell ls -la $(COVERAGE_DIR) 2>/dev/null | wc -l || echo '0') files"
