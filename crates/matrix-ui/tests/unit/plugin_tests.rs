//! Unit tests per il sistema di plugin
//!
//! Questi test verificano il corretto funzionamento del PluginHost
//! e dei componenti correlati.

use std::sync::Arc;
use matrix_ui::plugin_host::{PluginHost, PluginInfo, PluginStatus};
use matrix_ui::theme::ThemeManager;
use matrix_core::Engine as CoreEngine;
use rstest::*;
use tokio_test;

#[fixture]
fn theme_manager() -> Arc<ThemeManager> {
    Arc::new(ThemeManager::new().unwrap())
}

#[fixture]
async fn core_engine() -> Arc<CoreEngine> {
    Arc::new(CoreEngine::new().await.unwrap())
}

#[fixture]
async fn plugin_host(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) -> PluginHost {
    let core = core_engine.await;
    PluginHost::new(core, theme_manager).unwrap()
}

#[rstest]
#[tokio::test]
async fn test_plugin_host_creation(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let result = PluginHost::new(core, theme_manager);
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_plugin_registration(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info = PluginInfo {
        id: "test-plugin".to_string(),
        name: "Test Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "A test plugin".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    let result = host.register_plugin(plugin_info);
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_plugin_duplicate_registration(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info1 = PluginInfo {
        id: "duplicate-plugin".to_string(),
        name: "First Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "First plugin".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    let plugin_info2 = PluginInfo {
        id: "duplicate-plugin".to_string(), // Stesso ID
        name: "Second Plugin".to_string(),
        version: "2.0.0".to_string(),
        description: "Second plugin".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    // Prima registrazione OK
    assert!(host.register_plugin(plugin_info1).is_ok());
    
    // Seconda registrazione dovrebbe fallire
    assert!(host.register_plugin(plugin_info2).is_err());
}

#[rstest]
#[tokio::test]
async fn test_plugin_activation(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info = PluginInfo {
        id: "activation-test".to_string(),
        name: "Activation Test Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "Plugin for testing activation".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    host.register_plugin(plugin_info).unwrap();
    
    // Test attivazione
    let result = host.activate_plugin("activation-test");
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_plugin_deactivation(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info = PluginInfo {
        id: "deactivation-test".to_string(),
        name: "Deactivation Test Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "Plugin for testing deactivation".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    host.register_plugin(plugin_info).unwrap();
    host.activate_plugin("deactivation-test").unwrap();
    
    // Test disattivazione
    let result = host.deactivate_plugin("deactivation-test");
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_plugin_nonexistent_operations(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    // Test operazioni su plugin inesistenti
    assert!(host.activate_plugin("nonexistent").is_err());
    assert!(host.deactivate_plugin("nonexistent").is_err());
}

#[rstest]
#[tokio::test]
async fn test_plugin_status_management(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info = PluginInfo {
        id: "status-test".to_string(),
        name: "Status Test Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "Plugin for testing status".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    host.register_plugin(plugin_info).unwrap();
    
    // Test ciclo di vita completo
    assert!(host.activate_plugin("status-test").is_ok());
    assert!(host.deactivate_plugin("status-test").is_ok());
    assert!(host.activate_plugin("status-test").is_ok());
}

#[rstest]
#[tokio::test]
async fn test_plugin_info_validation(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    // Test con ID vuoto
    let empty_id_plugin = PluginInfo {
        id: "".to_string(),
        name: "Empty ID Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "Plugin with empty ID".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    // Dovrebbe fallire con ID vuoto
    assert!(host.register_plugin(empty_id_plugin).is_err());
}

#[rstest]
#[tokio::test]
async fn test_plugin_version_handling(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let versions = vec!["1.0.0", "2.0.0-beta", "3.0.0-alpha.1", "0.1.0"];
    
    for (i, version) in versions.iter().enumerate() {
        let plugin_info = PluginInfo {
            id: format!("version-test-{}", i),
            name: format!("Version Test Plugin {}", i),
            version: version.to_string(),
            description: "Plugin for testing versions".to_string(),
            author: "Test Author".to_string(),
            status: PluginStatus::Inactive,
        };
        
        assert!(host.register_plugin(plugin_info).is_ok());
    }
}

#[rstest]
#[tokio::test]
async fn test_plugin_batch_operations(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    // Registra multipli plugin
    for i in 0..10 {
        let plugin_info = PluginInfo {
            id: format!("batch-plugin-{}", i),
            name: format!("Batch Plugin {}", i),
            version: "1.0.0".to_string(),
            description: format!("Batch plugin number {}", i),
            author: "Test Author".to_string(),
            status: PluginStatus::Inactive,
        };
        
        assert!(host.register_plugin(plugin_info).is_ok());
    }
    
    // Attiva tutti i plugin
    for i in 0..10 {
        assert!(host.activate_plugin(&format!("batch-plugin-{}", i)).is_ok());
    }
    
    // Disattiva tutti i plugin
    for i in 0..10 {
        assert!(host.deactivate_plugin(&format!("batch-plugin-{}", i)).is_ok());
    }
}

#[test]
fn test_plugin_host_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    
    tokio_test::block_on(async {
        let core = Arc::new(CoreEngine::new().await.unwrap());
        let theme_manager = Arc::new(ThemeManager::new().unwrap());
        let host = Arc::new(PluginHost::new(core, theme_manager).unwrap());
        
        let mut handles = vec![];
        
        for i in 0..10 {
            let host_clone = Arc::clone(&host);
            let handle = thread::spawn(move || {
                let plugin_info = PluginInfo {
                    id: format!("thread-plugin-{}", i),
                    name: format!("Thread Plugin {}", i),
                    version: "1.0.0".to_string(),
                    description: format!("Thread plugin {}", i),
                    author: "Test Author".to_string(),
                    status: PluginStatus::Inactive,
                };
                
                host_clone.register_plugin(plugin_info).unwrap();
                host_clone.activate_plugin(&format!("thread-plugin-{}", i)).unwrap();
                i
            });
            handles.push(handle);
        }
        
        for handle in handles {
            handle.join().unwrap();
        }
    });
}

#[rstest]
#[tokio::test]
async fn test_plugin_error_recovery(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    let plugin_info = PluginInfo {
        id: "recovery-test".to_string(),
        name: "Recovery Test Plugin".to_string(),
        version: "1.0.0".to_string(),
        description: "Plugin for testing error recovery".to_string(),
        author: "Test Author".to_string(),
        status: PluginStatus::Inactive,
    };
    
    host.register_plugin(plugin_info).unwrap();
    
    // Test recovery da errori
    let _ = host.activate_plugin("nonexistent"); // Errore
    assert!(host.activate_plugin("recovery-test").is_ok()); // Dovrebbe funzionare
}

#[rstest]
#[tokio::test]
async fn test_plugin_memory_management(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    
    // Test che non ci siano memory leak
    for i in 0..100 {
        let host = PluginHost::new(core.clone(), theme_manager.clone()).unwrap();
        
        let plugin_info = PluginInfo {
            id: format!("memory-test-{}", i),
            name: format!("Memory Test Plugin {}", i),
            version: "1.0.0".to_string(),
            description: "Plugin for memory testing".to_string(),
            author: "Test Author".to_string(),
            status: PluginStatus::Inactive,
        };
        
        host.register_plugin(plugin_info).unwrap();
        // Il host dovrebbe essere deallocato automaticamente
    }
    
    assert!(true);
}

#[rstest]
#[tokio::test]
async fn test_plugin_performance(
    #[future] plugin_host: PluginHost
) {
    let host = plugin_host.await;
    
    use std::time::Instant;
    
    // Test performance con molte operazioni
    let start = Instant::now();
    
    for i in 0..1000 {
        let plugin_info = PluginInfo {
            id: format!("perf-plugin-{}", i),
            name: format!("Performance Plugin {}", i),
            version: "1.0.0".to_string(),
            description: "Performance test plugin".to_string(),
            author: "Test Author".to_string(),
            status: PluginStatus::Inactive,
        };
        
        host.register_plugin(plugin_info).unwrap();
    }
    
    let duration = start.elapsed();
    
    // Le operazioni dovrebbero essere veloci
    assert!(duration.as_secs() < 1);
}

#[rstest]
#[tokio::test]
async fn test_plugin_status_types() {
    // Test tutti i tipi di status
    let statuses = vec![
        PluginStatus::Inactive,
        PluginStatus::Active,
        PluginStatus::Error,
        PluginStatus::Loading,
    ];
    
    for status in statuses {
        let plugin_info = PluginInfo {
            id: format!("status-{:?}", status),
            name: format!("Status {:?} Plugin", status),
            version: "1.0.0".to_string(),
            description: format!("Plugin with {:?} status", status),
            author: "Test Author".to_string(),
            status,
        };
        
        // Il plugin dovrebbe essere creato correttamente
        assert!(!plugin_info.id.is_empty());
    }
}
