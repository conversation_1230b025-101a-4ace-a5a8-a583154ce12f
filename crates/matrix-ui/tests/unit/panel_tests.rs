//! Unit tests per il sistema di pannelli
//!
//! Questi test verificano il corretto funzionamento del PanelManager
//! e dei componenti correlati.

use std::sync::Arc;
use matrix_ui::theme::ThemeManager;
use matrix_ui::panels::{PanelManager, Panel, PanelType};
use rstest::*;
use serial_test::serial;

#[fixture]
fn theme_manager() -> Arc<ThemeManager> {
    Arc::new(ThemeManager::new().unwrap())
}

#[fixture]
fn panel_manager(theme_manager: Arc<ThemeManager>) -> PanelManager {
    PanelManager::new(theme_manager)
}

#[fixture]
fn test_panel() -> Panel {
    Panel {
        id: "test-panel".to_string(),
        title: "Test Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    }
}

#[rstest]
fn test_panel_manager_creation(panel_manager: PanelManager) {
    // Il panel manager dovrebbe essere creato correttamente
    assert!(true);
}

#[rstest]
fn test_panel_registration(panel_manager: PanelManager, test_panel: Panel) {
    let result = panel_manager.register_panel(test_panel);
    assert!(result.is_ok());
}

#[rstest]
fn test_panel_duplicate_registration(panel_manager: PanelManager) {
    let panel1 = Panel {
        id: "duplicate".to_string(),
        title: "First Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    let panel2 = Panel {
        id: "duplicate".to_string(), // Stesso ID
        title: "Second Panel".to_string(),
        panel_type: PanelType::Right,
        visible: true,
        content: None,
    };
    
    // Prima registrazione OK
    assert!(panel_manager.register_panel(panel1).is_ok());
    
    // Seconda registrazione dovrebbe fallire
    assert!(panel_manager.register_panel(panel2).is_err());
}

#[rstest]
fn test_panel_visibility_management(panel_manager: PanelManager) {
    let panel = Panel {
        id: "visibility-test".to_string(),
        title: "Visibility Test".to_string(),
        panel_type: PanelType::Right,
        visible: true,
        content: None,
    };
    
    panel_manager.register_panel(panel).unwrap();
    
    // Test nascondere pannello
    assert!(panel_manager.hide_panel("visibility-test").is_ok());
    
    // Test mostrare pannello
    assert!(panel_manager.show_panel("visibility-test").is_ok());
}

#[rstest]
fn test_panel_nonexistent_operations(panel_manager: PanelManager) {
    // Test operazioni su pannelli inesistenti
    assert!(panel_manager.hide_panel("nonexistent").is_err());
    assert!(panel_manager.show_panel("nonexistent").is_err());
}

#[rstest]
fn test_panel_types() {
    let types = vec![
        PanelType::Left,
        PanelType::Right,
        PanelType::Bottom,
        PanelType::Center,
    ];
    
    for panel_type in types {
        let panel = Panel {
            id: format!("test-{:?}", panel_type),
            title: format!("Test {:?} Panel", panel_type),
            panel_type,
            visible: true,
            content: None,
        };
        
        assert!(!panel.id.is_empty());
        assert!(!panel.title.is_empty());
    }
}

#[rstest]
fn test_panel_state_management(panel_manager: PanelManager) {
    let panel = Panel {
        id: "state-test".to_string(),
        title: "State Test Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    panel_manager.register_panel(panel).unwrap();
    
    // Test multiple state changes
    for _ in 0..5 {
        assert!(panel_manager.hide_panel("state-test").is_ok());
        assert!(panel_manager.show_panel("state-test").is_ok());
    }
}

#[test]
#[serial]
fn test_panel_manager_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let panel_manager = Arc::new(PanelManager::new(theme_manager));
    let mut handles = vec![];
    
    for i in 0..10 {
        let pm_clone = Arc::clone(&panel_manager);
        let handle = thread::spawn(move || {
            let panel = Panel {
                id: format!("thread-panel-{}", i),
                title: format!("Thread Panel {}", i),
                panel_type: PanelType::Left,
                visible: true,
                content: None,
            };
            
            pm_clone.register_panel(panel).unwrap();
            i
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
}

#[rstest]
fn test_panel_content_management(panel_manager: PanelManager) {
    let mut panel = Panel {
        id: "content-test".to_string(),
        title: "Content Test Panel".to_string(),
        panel_type: PanelType::Center,
        visible: true,
        content: None,
    };
    
    // Test con contenuto None
    assert!(panel.content.is_none());
    
    // Test aggiunta contenuto (simulato)
    panel.content = Some("Test Content".to_string());
    assert!(panel.content.is_some());
    
    panel_manager.register_panel(panel).unwrap();
}

#[rstest]
fn test_panel_id_validation(panel_manager: PanelManager) {
    // Test con ID vuoto
    let empty_panel = Panel {
        id: "".to_string(),
        title: "Empty ID Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    // Dovrebbe fallire con ID vuoto
    assert!(panel_manager.register_panel(empty_panel).is_err());
}

#[rstest]
fn test_panel_title_validation(panel_manager: PanelManager) {
    // Test con titolo vuoto
    let empty_title_panel = Panel {
        id: "empty-title".to_string(),
        title: "".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    // Dovrebbe essere accettato (titolo vuoto potrebbe essere valido)
    assert!(panel_manager.register_panel(empty_title_panel).is_ok());
}

#[rstest]
fn test_panel_batch_operations(panel_manager: PanelManager) {
    // Test registrazione multipla
    let panels = vec![
        Panel {
            id: "batch-1".to_string(),
            title: "Batch Panel 1".to_string(),
            panel_type: PanelType::Left,
            visible: true,
            content: None,
        },
        Panel {
            id: "batch-2".to_string(),
            title: "Batch Panel 2".to_string(),
            panel_type: PanelType::Right,
            visible: true,
            content: None,
        },
        Panel {
            id: "batch-3".to_string(),
            title: "Batch Panel 3".to_string(),
            panel_type: PanelType::Bottom,
            visible: true,
            content: None,
        },
    ];
    
    for panel in panels {
        assert!(panel_manager.register_panel(panel).is_ok());
    }
    
    // Test operazioni batch
    assert!(panel_manager.hide_panel("batch-1").is_ok());
    assert!(panel_manager.hide_panel("batch-2").is_ok());
    assert!(panel_manager.hide_panel("batch-3").is_ok());
    
    assert!(panel_manager.show_panel("batch-1").is_ok());
    assert!(panel_manager.show_panel("batch-2").is_ok());
    assert!(panel_manager.show_panel("batch-3").is_ok());
}

#[rstest]
fn test_panel_memory_management(panel_manager: PanelManager) {
    // Test che i pannelli vengano gestiti correttamente in memoria
    for i in 0..100 {
        let panel = Panel {
            id: format!("memory-test-{}", i),
            title: format!("Memory Test Panel {}", i),
            panel_type: PanelType::Left,
            visible: true,
            content: Some(format!("Content {}", i)),
        };
        
        assert!(panel_manager.register_panel(panel).is_ok());
    }
    
    // Tutti i pannelli dovrebbero essere registrati correttamente
    assert!(true);
}

#[rstest]
fn test_panel_error_recovery(panel_manager: PanelManager) {
    // Test recovery da errori
    let panel = Panel {
        id: "recovery-test".to_string(),
        title: "Recovery Test Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    panel_manager.register_panel(panel).unwrap();
    
    // Test operazioni dopo errore
    let _ = panel_manager.hide_panel("nonexistent"); // Errore
    assert!(panel_manager.hide_panel("recovery-test").is_ok()); // Dovrebbe funzionare
}

#[rstest]
fn test_panel_concurrent_access(panel_manager: PanelManager) {
    use std::sync::Arc;
    use std::thread;
    
    let pm = Arc::new(panel_manager);
    let mut handles = vec![];
    
    // Registra un pannello
    let panel = Panel {
        id: "concurrent-test".to_string(),
        title: "Concurrent Test Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    pm.register_panel(panel).unwrap();
    
    // Test accesso concorrente
    for i in 0..5 {
        let pm_clone = Arc::clone(&pm);
        let handle = thread::spawn(move || {
            if i % 2 == 0 {
                pm_clone.hide_panel("concurrent-test").unwrap();
            } else {
                pm_clone.show_panel("concurrent-test").unwrap();
            }
            i
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
}
