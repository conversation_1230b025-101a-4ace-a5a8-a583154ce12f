//! Unit tests per il sistema di temi
//!
//! Questi test verificano il corretto funzionamento del ThemeManager
//! e dei componenti correlati.

use std::sync::Arc;
use matrix_ui::theme::{ThemeManager, Theme, ThemeColors, FontSizes, Spacing, BorderRadius};
use floem::style::Color;
use tempfile::TempDir;
use rstest::*;
use serial_test::serial;

/// Fixture per creare un ThemeManager di test
#[fixture]
fn theme_manager() -> Result<ThemeManager, Box<dyn std::error::Error>> {
    ThemeManager::new()
}

/// Fixture per creare un tema di test
#[fixture]
fn test_theme() -> Theme {
    let colors = ThemeColors {
        background: Color::rgb8(0x21, 0x21, 0x21),
        background_secondary: Color::rgb8(0x2D, 0x2D, 0x2D),
        background_tertiary: Color::rgb8(0x3D, 0x3D, 0x3D),
        text: Color::rgb8(0xE0, 0xE0, 0xE0),
        text_secondary: Color::rgb8(0xA0, 0xA0, 0xA0),
        text_tertiary: Color::rgb8(0x80, 0x80, 0x80),
        accent: Color::rgb8(0x00, 0x7A, 0xCC),
        accent_secondary: Color::rgb8(0x00, 0x5A, 0x9C),
        border: Color::rgb8(0x40, 0x40, 0x40),
        error: Color::rgb8(0xF4, 0x43, 0x36),
        warning: Color::rgb8(0xFF, 0x98, 0x00),
        success: Color::rgb8(0x4C, 0xAF, 0x50),
        info: Color::rgb8(0x21, 0x96, 0xF3),
        selection: Color::rgb8(0x26, 0x4F, 0x78),
        highlight: Color::rgb8(0xFF, 0xFF, 0x00),
    };
    
    Theme {
        name: "Test Theme".to_string(),
        colors,
        font_sizes: FontSizes::default(),
        spacing: Spacing::default(),
        border_radius: BorderRadius::default(),
    }
}

#[rstest]
fn test_theme_manager_creation(theme_manager: Result<ThemeManager, Box<dyn std::error::Error>>) {
    assert!(theme_manager.is_ok());
}

#[rstest]
fn test_theme_manager_get_active_theme(theme_manager: Result<ThemeManager, Box<dyn std::error::Error>>) {
    let manager = theme_manager.unwrap();
    let theme = manager.get_active_theme();
    assert!(!theme.name.is_empty());
}

#[rstest]
fn test_theme_serialization(test_theme: Theme) {
    let json = serde_json::to_string(&test_theme);
    assert!(json.is_ok());
    
    let json_str = json.unwrap();
    assert!(json_str.contains("Test Theme"));
    assert!(json_str.contains("colors"));
}

#[rstest]
fn test_theme_deserialization(test_theme: Theme) {
    let json = serde_json::to_string(&test_theme).unwrap();
    let deserialized: Result<Theme, _> = serde_json::from_str(&json);
    
    assert!(deserialized.is_ok());
    let theme = deserialized.unwrap();
    assert_eq!(theme.name, "Test Theme");
}

#[rstest]
fn test_theme_colors_access(test_theme: Theme) {
    assert_eq!(test_theme.colors.background, Color::rgb8(0x21, 0x21, 0x21));
    assert_eq!(test_theme.colors.text, Color::rgb8(0xE0, 0xE0, 0xE0));
    assert_eq!(test_theme.colors.accent, Color::rgb8(0x00, 0x7A, 0xCC));
}

#[rstest]
fn test_theme_clone(test_theme: Theme) {
    let cloned = test_theme.clone();
    assert_eq!(cloned.name, test_theme.name);
    assert_eq!(cloned.colors.background, test_theme.colors.background);
}

#[rstest]
fn test_font_sizes_default() {
    let font_sizes = FontSizes::default();
    assert!(font_sizes.xs > 0.0);
    assert!(font_sizes.sm > font_sizes.xs);
    assert!(font_sizes.md > font_sizes.sm);
    assert!(font_sizes.lg > font_sizes.md);
    assert!(font_sizes.xl > font_sizes.lg);
    assert!(font_sizes.xxl > font_sizes.xl);
}

#[rstest]
fn test_spacing_default() {
    let spacing = Spacing::default();
    assert!(spacing.xs > 0.0);
    assert!(spacing.sm > spacing.xs);
    assert!(spacing.md > spacing.sm);
    assert!(spacing.lg > spacing.md);
    assert!(spacing.xl > spacing.lg);
    assert!(spacing.xxl > spacing.xl);
}

#[rstest]
fn test_border_radius_default() {
    let border_radius = BorderRadius::default();
    assert!(border_radius.sm > 0.0);
    assert!(border_radius.md > border_radius.sm);
    assert!(border_radius.lg > border_radius.md);
    assert!(border_radius.xl > border_radius.lg);
}

#[test]
#[serial]
fn test_theme_manager_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    
    let manager = Arc::new(ThemeManager::new().unwrap());
    let mut handles = vec![];
    
    for i in 0..10 {
        let manager_clone = Arc::clone(&manager);
        let handle = thread::spawn(move || {
            let theme = manager_clone.get_active_theme();
            assert!(!theme.name.is_empty());
            i
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
}

#[rstest]
fn test_color_conversion() {
    let color = Color::rgb8(0x21, 0x21, 0x21);
    let rgba = color.to_rgba8();
    
    assert_eq!(rgba[0], 0x21);
    assert_eq!(rgba[1], 0x21);
    assert_eq!(rgba[2], 0x21);
    assert_eq!(rgba[3], 0xFF); // Alpha should be 255
}

#[rstest]
fn test_theme_validation(test_theme: Theme) {
    // Test che il tema sia valido
    assert!(!test_theme.name.is_empty());
    assert!(test_theme.font_sizes.md > 0.0);
    assert!(test_theme.spacing.md > 0.0);
    assert!(test_theme.border_radius.md > 0.0);
}

#[test]
fn test_theme_manager_error_handling() {
    // Test gestione errori quando directory non esiste
    // Questo test dovrebbe essere implementato quando avremo
    // un sistema di configurazione più robusto
}

#[rstest]
fn test_theme_modification(mut test_theme: Theme) {
    let original_bg = test_theme.colors.background;
    test_theme.colors.background = Color::rgb8(0x30, 0x30, 0x30);
    
    assert_ne!(test_theme.colors.background, original_bg);
    assert_eq!(test_theme.colors.background, Color::rgb8(0x30, 0x30, 0x30));
}

#[rstest]
fn test_theme_equality(test_theme: Theme) {
    let cloned = test_theme.clone();
    
    // Test che i temi clonati siano uguali
    assert_eq!(test_theme.name, cloned.name);
    assert_eq!(test_theme.colors.background, cloned.colors.background);
    assert_eq!(test_theme.font_sizes.md, cloned.font_sizes.md);
}

#[test]
fn test_multiple_theme_managers() {
    let manager1 = ThemeManager::new().unwrap();
    let manager2 = ThemeManager::new().unwrap();
    
    let theme1 = manager1.get_active_theme();
    let theme2 = manager2.get_active_theme();
    
    // I temi dovrebbero essere identici
    assert_eq!(theme1.name, theme2.name);
}

#[rstest]
fn test_theme_json_roundtrip(test_theme: Theme) {
    // Test serializzazione -> deserializzazione -> serializzazione
    let json1 = serde_json::to_string(&test_theme).unwrap();
    let theme_copy: Theme = serde_json::from_str(&json1).unwrap();
    let json2 = serde_json::to_string(&theme_copy).unwrap();
    
    assert_eq!(json1, json2);
}

#[test]
fn test_theme_manager_singleton_behavior() {
    // Test che il ThemeManager si comporti come un singleton
    let manager1 = ThemeManager::new().unwrap();
    let manager2 = ThemeManager::new().unwrap();
    
    let theme1 = manager1.get_active_theme();
    let theme2 = manager2.get_active_theme();
    
    assert_eq!(theme1.name, theme2.name);
}
