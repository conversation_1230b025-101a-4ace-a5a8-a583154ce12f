//! Unit tests per i componenti UI
//!
//! Questo modulo contiene tutti i test unitari per i componenti
//! dell'interfaccia utente di MATRIX IDE.
//!
//! ## Struttura dei test
//!
//! - `theme_tests`: Test per il sistema di temi
//! - `component_tests`: Test per i componenti UI generici
//! - `layout_tests`: Test per il sistema di layout
//! - `panel_tests`: Test per il sistema di pannelli
//! - `dag_tests`: Test per il visualizzatore DAG
//! - `plugin_tests`: Test per il sistema di plugin
//!
//! ## Esecuzione dei test
//!
//! ```bash
//! # Esegui tutti i test unitari
//! cargo test --lib
//!
//! # Esegui test specifici per modulo
//! cargo test theme_tests
//! cargo test component_tests
//! cargo test layout_tests
//! cargo test panel_tests
//! cargo test dag_tests
//! cargo test plugin_tests
//!
//! # Esegui test con output dettagliato
//! cargo test -- --nocapture
//!
//! # Esegui test in parallelo
//! cargo test -- --test-threads=4
//! ```

pub mod theme_tests;
pub mod component_tests;
pub mod layout_tests;
pub mod panel_tests;
pub mod dag_tests;
pub mod plugin_tests;
