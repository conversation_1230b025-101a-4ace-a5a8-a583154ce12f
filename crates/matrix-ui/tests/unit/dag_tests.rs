//! Unit tests per il sistema DAG
//!
//! Questi test verificano il corretto funzionamento del DagViewer
//! e dei componenti correlati.

use std::sync::Arc;
use matrix_ui::theme::ThemeManager;
use matrix_ui::components::dag_viewer::DagViewer;
use matrix_core::Engine as CoreEngine;
use rstest::*;
use tokio_test;

#[fixture]
fn theme_manager() -> Arc<ThemeManager> {
    Arc::new(ThemeManager::new().unwrap())
}

#[fixture]
async fn core_engine() -> Arc<CoreEngine> {
    Arc::new(CoreEngine::new().await.unwrap())
}

#[fixture]
async fn dag_viewer(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) -> DagViewer {
    let core = core_engine.await;
    DagViewer::new(core, theme_manager).unwrap()
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_creation(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let result = DagViewer::new(core, theme_manager);
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_node_selection(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test selezione nodo
    viewer.select_node(Some("test-node".to_string()));
    assert_eq!(viewer.get_selected_node(), Some("test-node".to_string()));
    
    // Test deselezione
    viewer.select_node(None);
    assert_eq!(viewer.get_selected_node(), None);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_graph_management(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test impostazione grafo
    viewer.set_current_graph(Some("test-graph".to_string()));
    assert_eq!(viewer.get_current_graph(), Some("test-graph".to_string()));
    
    // Test reset grafo
    viewer.set_current_graph(None);
    assert_eq!(viewer.get_current_graph(), None);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_multiple_nodes(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test selezione multipla di nodi
    let nodes = vec!["node-1", "node-2", "node-3"];
    
    for node in &nodes {
        viewer.select_node(Some(node.to_string()));
        assert_eq!(viewer.get_selected_node(), Some(node.to_string()));
    }
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_graph_switching(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test cambio rapido di grafi
    let graphs = vec!["graph-1", "graph-2", "graph-3"];
    
    for graph in &graphs {
        viewer.set_current_graph(Some(graph.to_string()));
        assert_eq!(viewer.get_current_graph(), Some(graph.to_string()));
    }
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_state_consistency(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test consistenza stato
    viewer.set_current_graph(Some("consistency-graph".to_string()));
    viewer.select_node(Some("consistency-node".to_string()));
    
    // Verifica che lo stato sia mantenuto
    assert_eq!(viewer.get_current_graph(), Some("consistency-graph".to_string()));
    assert_eq!(viewer.get_selected_node(), Some("consistency-node".to_string()));
    
    // Test reset parziale
    viewer.select_node(None);
    assert_eq!(viewer.get_current_graph(), Some("consistency-graph".to_string()));
    assert_eq!(viewer.get_selected_node(), None);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_error_handling(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test gestione nodi inesistenti
    viewer.select_node(Some("nonexistent-node".to_string()));
    // Non dovrebbe causare panic
    assert!(true);
    
    // Test gestione grafi inesistenti
    viewer.set_current_graph(Some("nonexistent-graph".to_string()));
    // Non dovrebbe causare panic
    assert!(true);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_empty_strings(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test con stringhe vuote
    viewer.select_node(Some("".to_string()));
    viewer.set_current_graph(Some("".to_string()));
    
    // Dovrebbe gestire gracefully
    assert!(true);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_long_strings(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test con stringhe molto lunghe
    let long_string = "a".repeat(10000);
    
    viewer.select_node(Some(long_string.clone()));
    viewer.set_current_graph(Some(long_string.clone()));
    
    // Dovrebbe gestire stringhe lunghe
    assert!(true);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_special_characters(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test con caratteri speciali
    let special_chars = vec![
        "node-with-spaces and symbols!@#$%^&*()",
        "node_with_unicode_🚀_🎯_⚡",
        "node/with/slashes",
        "node\\with\\backslashes",
        "node\"with\"quotes",
        "node'with'apostrophes",
    ];
    
    for special_char in special_chars {
        viewer.select_node(Some(special_char.to_string()));
        viewer.set_current_graph(Some(special_char.to_string()));
        // Dovrebbe gestire caratteri speciali
        assert!(true);
    }
}

#[test]
fn test_dag_viewer_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    
    tokio_test::block_on(async {
        let core = Arc::new(CoreEngine::new().await.unwrap());
        let theme_manager = Arc::new(ThemeManager::new().unwrap());
        let viewer = Arc::new(DagViewer::new(core, theme_manager).unwrap());
        
        let mut handles = vec![];
        
        for i in 0..10 {
            let viewer_clone = Arc::clone(&viewer);
            let handle = thread::spawn(move || {
                viewer_clone.select_node(Some(format!("thread-node-{}", i)));
                viewer_clone.set_current_graph(Some(format!("thread-graph-{}", i)));
                i
            });
            handles.push(handle);
        }
        
        for handle in handles {
            handle.join().unwrap();
        }
    });
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_performance(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    use std::time::Instant;
    
    // Test performance con molte operazioni
    let start = Instant::now();
    
    for i in 0..1000 {
        viewer.select_node(Some(format!("perf-node-{}", i)));
        viewer.set_current_graph(Some(format!("perf-graph-{}", i)));
    }
    
    let duration = start.elapsed();
    
    // Le operazioni dovrebbero essere veloci
    assert!(duration.as_secs() < 1);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_memory_usage(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    
    // Test che non ci siano memory leak
    for _ in 0..100 {
        let viewer = DagViewer::new(core.clone(), theme_manager.clone()).unwrap();
        viewer.select_node(Some("memory-test-node".to_string()));
        viewer.set_current_graph(Some("memory-test-graph".to_string()));
        // Il viewer dovrebbe essere deallocato automaticamente
    }
    
    assert!(true);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_state_transitions(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test transizioni di stato complesse
    viewer.set_current_graph(Some("graph-1".to_string()));
    viewer.select_node(Some("node-1".to_string()));
    
    viewer.set_current_graph(Some("graph-2".to_string()));
    // Il nodo selezionato dovrebbe essere mantenuto o resettato?
    // Dipende dall'implementazione
    
    viewer.select_node(Some("node-2".to_string()));
    viewer.set_current_graph(None);
    
    // Test stato finale
    assert_eq!(viewer.get_current_graph(), None);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_concurrent_operations(
    #[future] dag_viewer: DagViewer
) {
    let viewer = Arc::new(dag_viewer.await);
    
    use std::thread;
    let mut handles = vec![];
    
    // Test operazioni concorrenti
    for i in 0..5 {
        let viewer_clone = Arc::clone(&viewer);
        let handle = thread::spawn(move || {
            if i % 2 == 0 {
                viewer_clone.select_node(Some(format!("concurrent-node-{}", i)));
            } else {
                viewer_clone.set_current_graph(Some(format!("concurrent-graph-{}", i)));
            }
            i
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_edge_cases(
    #[future] dag_viewer: DagViewer
) {
    let viewer = dag_viewer.await;
    
    // Test casi edge
    viewer.select_node(None);
    viewer.select_node(None); // Doppia deselezione
    
    viewer.set_current_graph(None);
    viewer.set_current_graph(None); // Doppio reset
    
    // Test alternanza rapida
    for _ in 0..10 {
        viewer.select_node(Some("edge-node".to_string()));
        viewer.select_node(None);
    }
    
    assert!(true);
}
