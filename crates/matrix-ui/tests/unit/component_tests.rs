//! Unit tests per i componenti UI
//!
//! Questi test verificano il corretto funzionamento dei componenti
//! dell'interfaccia utente.

use std::sync::Arc;
use matrix_ui::theme::ThemeManager;
use matrix_ui::components::dag_viewer::DagViewer;
use matrix_ui::panels::{PanelManager, Panel, PanelType};
use matrix_core::Engine as CoreEngine;
use rstest::*;
use tokio_test;

/// Fixture per creare un ThemeManager di test
#[fixture]
fn theme_manager() -> Arc<ThemeManager> {
    Arc::new(ThemeManager::new().unwrap())
}

/// Fixture per creare un CoreEngine di test
#[fixture]
async fn core_engine() -> Arc<CoreEngine> {
    Arc::new(CoreEngine::new().await.unwrap())
}

/// Fixture per creare un PanelManager di test
#[fixture]
fn panel_manager(theme_manager: Arc<ThemeManager>) -> PanelManager {
    PanelManager::new(theme_manager)
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_creation(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let result = DagViewer::new(core, theme_manager);
    assert!(result.is_ok());
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_node_selection(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let dag_viewer = DagViewer::new(core, theme_manager).unwrap();
    
    // Test selezione nodo
    dag_viewer.select_node(Some("test-node".to_string()));
    assert_eq!(dag_viewer.get_selected_node(), Some("test-node".to_string()));
    
    // Test deselezione
    dag_viewer.select_node(None);
    assert_eq!(dag_viewer.get_selected_node(), None);
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_graph_switching(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let dag_viewer = DagViewer::new(core, theme_manager).unwrap();
    
    // Test cambio grafo
    dag_viewer.set_current_graph(Some("test-graph".to_string()));
    assert_eq!(dag_viewer.get_current_graph(), Some("test-graph".to_string()));
    
    // Test reset grafo
    dag_viewer.set_current_graph(None);
    assert_eq!(dag_viewer.get_current_graph(), None);
}

#[rstest]
fn test_panel_manager_creation(panel_manager: PanelManager) {
    // Il panel manager dovrebbe essere creato correttamente
    assert!(true); // Se arriviamo qui, la creazione è andata a buon fine
}

#[rstest]
fn test_panel_registration(panel_manager: PanelManager) {
    let panel = Panel {
        id: "test-panel".to_string(),
        title: "Test Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    let result = panel_manager.register_panel(panel);
    assert!(result.is_ok());
}

#[rstest]
fn test_panel_visibility_management(panel_manager: PanelManager) {
    // Registra un pannello
    let panel = Panel {
        id: "visibility-test".to_string(),
        title: "Visibility Test Panel".to_string(),
        panel_type: PanelType::Right,
        visible: true,
        content: None,
    };
    
    panel_manager.register_panel(panel).unwrap();
    
    // Test nascondere pannello
    let result = panel_manager.hide_panel("visibility-test");
    assert!(result.is_ok());
    
    // Test mostrare pannello
    let result = panel_manager.show_panel("visibility-test");
    assert!(result.is_ok());
}

#[rstest]
fn test_panel_duplicate_registration(panel_manager: PanelManager) {
    let panel1 = Panel {
        id: "duplicate-test".to_string(),
        title: "First Panel".to_string(),
        panel_type: PanelType::Left,
        visible: true,
        content: None,
    };
    
    let panel2 = Panel {
        id: "duplicate-test".to_string(), // Stesso ID
        title: "Second Panel".to_string(),
        panel_type: PanelType::Right,
        visible: true,
        content: None,
    };
    
    // Prima registrazione dovrebbe andare a buon fine
    assert!(panel_manager.register_panel(panel1).is_ok());
    
    // Seconda registrazione con stesso ID dovrebbe fallire
    assert!(panel_manager.register_panel(panel2).is_err());
}

#[rstest]
fn test_panel_nonexistent_operations(panel_manager: PanelManager) {
    // Test operazioni su pannelli inesistenti
    assert!(panel_manager.hide_panel("nonexistent").is_err());
    assert!(panel_manager.show_panel("nonexistent").is_err());
}

#[rstest]
fn test_panel_types() {
    // Test che tutti i tipi di pannello siano validi
    let types = vec![
        PanelType::Left,
        PanelType::Right,
        PanelType::Bottom,
        PanelType::Center,
    ];
    
    for panel_type in types {
        let panel = Panel {
            id: format!("test-{:?}", panel_type),
            title: format!("Test {:?} Panel", panel_type),
            panel_type,
            visible: true,
            content: None,
        };
        
        // Il pannello dovrebbe essere creato correttamente
        assert!(!panel.id.is_empty());
        assert!(!panel.title.is_empty());
    }
}

#[test]
fn test_button_component_creation() {
    use matrix_ui::components::button::{ButtonType, ButtonSize};
    
    // Test creazione enum ButtonType
    let button_types = vec![
        ButtonType::Primary,
        ButtonType::Secondary,
        ButtonType::Danger,
        ButtonType::Success,
        ButtonType::Warning,
        ButtonType::Info,
    ];
    
    for button_type in button_types {
        // Gli enum dovrebbero essere creati correttamente
        assert!(true);
    }
    
    // Test creazione enum ButtonSize
    let button_sizes = vec![
        ButtonSize::Small,
        ButtonSize::Medium,
        ButtonSize::Large,
    ];
    
    for button_size in button_sizes {
        // Gli enum dovrebbero essere creati correttamente
        assert!(true);
    }
}

#[test]
fn test_status_bar_component() {
    use matrix_ui::components::status_bar::{StatusType, StatusItem};
    use floem::reactive::RwSignal;
    
    // Test creazione StatusType
    let status_types = vec![
        StatusType::Info,
        StatusType::Success,
        StatusType::Warning,
        StatusType::Error,
    ];
    
    for status_type in status_types {
        let status_item = StatusItem::new(
            RwSignal::new("Test".to_string()),
            RwSignal::new(status_type),
        );
        
        // Il StatusItem dovrebbe essere creato correttamente
        assert!(true);
    }
}

#[test]
fn test_tooltip_component() {
    use matrix_ui::components::tooltip::{TooltipPosition, TooltipConfig};
    
    // Test creazione TooltipPosition
    let positions = vec![
        TooltipPosition::Top,
        TooltipPosition::Bottom,
        TooltipPosition::Left,
        TooltipPosition::Right,
    ];
    
    for position in positions {
        let config = TooltipConfig {
            position,
            delay_ms: 500,
        };
        
        // Il TooltipConfig dovrebbe essere creato correttamente
        assert!(config.delay_ms > 0);
    }
}

#[rstest]
fn test_layout_manager_creation() {
    use matrix_ui::layout::LayoutManager;
    
    let layout_manager = LayoutManager::new();
    // Il layout manager dovrebbe essere creato correttamente
    assert!(true);
}

#[rstest]
fn test_layout_manager_update() {
    use matrix_ui::layout::LayoutManager;
    
    let mut layout_manager = LayoutManager::new();
    layout_manager.update_layout();
    
    // L'aggiornamento dovrebbe completarsi senza errori
    assert!(true);
}

#[test]
fn test_component_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    use matrix_ui::theme::ThemeManager;
    
    let theme_manager = Arc::new(ThemeManager::new().unwrap());
    let mut handles = vec![];
    
    for i in 0..5 {
        let theme_clone = Arc::clone(&theme_manager);
        let handle = thread::spawn(move || {
            let panel_manager = PanelManager::new(theme_clone);
            
            let panel = Panel {
                id: format!("thread-panel-{}", i),
                title: format!("Thread Panel {}", i),
                panel_type: PanelType::Left,
                visible: true,
                content: None,
            };
            
            panel_manager.register_panel(panel).unwrap();
            i
        });
        handles.push(handle);
    }
    
    for handle in handles {
        handle.join().unwrap();
    }
}

#[rstest]
#[tokio::test]
async fn test_dag_viewer_error_handling(
    #[future] core_engine: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>
) {
    let core = core_engine.await;
    let dag_viewer = DagViewer::new(core, theme_manager).unwrap();
    
    // Test gestione errori con nodi inesistenti
    dag_viewer.select_node(Some("nonexistent-node".to_string()));
    // Non dovrebbe causare panic
    assert!(true);
}

#[test]
fn test_component_memory_usage() {
    use matrix_ui::theme::ThemeManager;
    use matrix_ui::panels::PanelManager;
    
    // Test che i componenti non causino memory leak
    for _ in 0..100 {
        let theme_manager = Arc::new(ThemeManager::new().unwrap());
        let _panel_manager = PanelManager::new(theme_manager);
        // I componenti dovrebbero essere deallocati automaticamente
    }
    
    assert!(true);
}
