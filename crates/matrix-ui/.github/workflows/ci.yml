name: MATRIX IDE CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  test:
    name: Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        rust: [stable, beta]
        exclude:
          # Exclude beta on Windows and macOS for faster CI
          - os: windows-latest
            rust: beta
          - os: macos-latest
            rust: beta

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@master
      with:
        toolchain: ${{ matrix.rust }}
        components: rustfmt, clippy

    - name: Cache cargo registry
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install pkg-config

    - name: Check formatting
      run: cargo fmt --all -- --check

    - name: Run clippy
      run: cargo clippy --all-targets --all-features -- -D warnings

    - name: Build
      run: cargo build --verbose

    - name: Run unit tests
      run: cargo test --lib --verbose

    - name: Run integration tests
      run: cargo test --test integration_test --verbose

    - name: Run doc tests
      run: cargo test --doc

  coverage:
    name: Code Coverage
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev

    - name: Install tarpaulin
      run: cargo install cargo-tarpaulin

    - name: Generate coverage report
      run: cargo tarpaulin --verbose --all-features --workspace --timeout 300 --out xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: cobertura.xml
        fail_ci_if_error: false

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Install cargo-audit
      run: cargo install cargo-audit

    - name: Run security audit
      run: cargo audit

  benchmarks:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev

    - name: Run benchmarks
      run: |
        cargo bench --bench ui_performance
        cargo bench --bench theme_performance
        cargo bench --bench component_performance

    - name: Store benchmark results
      uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'cargo'
        output-file-path: target/criterion/reports/index.html
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true

  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    needs: [test, coverage, security]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev

    - name: Check code complexity
      run: |
        cargo install cargo-complexity
        cargo complexity --threshold 10

    - name: Check dependencies
      run: |
        cargo install cargo-machete
        cargo machete

    - name: Validate Cargo.toml
      run: |
        cargo install cargo-toml-lint
        cargo toml-lint

  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [test, coverage, security, quality-gates]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev

    - name: Build release
      run: cargo build --release

    - name: Create release artifacts
      run: |
        mkdir -p artifacts
        cp target/release/matrix-ui artifacts/
        tar -czf artifacts/matrix-ui-${{ github.sha }}.tar.gz -C target/release matrix-ui

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: matrix-ui-release
        path: artifacts/

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [test, coverage, security, quality-gates]
    if: always()
    steps:
    - name: Notify success
      if: needs.test.result == 'success' && needs.coverage.result == 'success' && needs.security.result == 'success'
      run: echo "✅ All checks passed!"

    - name: Notify failure
      if: needs.test.result == 'failure' || needs.coverage.result == 'failure' || needs.security.result == 'failure'
      run: |
        echo "❌ Some checks failed!"
        exit 1
