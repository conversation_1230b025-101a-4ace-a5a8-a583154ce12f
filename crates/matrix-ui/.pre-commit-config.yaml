# MATRIX IDE - Pre-commit Configuration
# 
# Questo file configura i pre-commit hooks per garantire
# la qualità del codice prima di ogni commit.

repos:
  # Rust-specific hooks
  - repo: local
    hooks:
      - id: cargo-fmt
        name: Cargo Format
        entry: cargo fmt --all --
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-clippy
        name: Cargo Clippy
        entry: cargo clippy --all-targets --all-features -- -D warnings
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-check
        name: Cargo Check
        entry: cargo check --all
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-test-unit
        name: Cargo Unit Tests
        entry: cargo test --lib
        language: system
        types: [rust]
        pass_filenames: false

      - id: cargo-audit
        name: Cargo Security Audit
        entry: cargo audit
        language: system
        types: [rust]
        pass_filenames: false

  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: '\.md$'
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Markdown hooks
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: ['--fix']

  # YAML hooks
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: ['-d', 'relaxed']

  # Security hooks
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']

# Configuration
default_language_version:
  python: python3

# Exclude patterns
exclude: |
  (?x)^(
    target/.*|
    \.git/.*|
    \.github/.*|
    docs/.*\.generated\..*
  )$

# Fail fast - stop on first failure
fail_fast: true

# Minimum pre-commit version
minimum_pre_commit_version: '3.0.0'
