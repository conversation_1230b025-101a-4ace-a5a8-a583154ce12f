#!/bin/bash

# MATRIX IDE - Test Automation Script
# 
# Questo script automatizza l'esecuzione di tutti i test per MATRIX IDE
# con reporting dettagliato e integrazione CI/CD.

set -euo pipefail

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurazione
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="${PROJECT_ROOT}/target/test-reports"
COVERAGE_DIR="${PROJECT_ROOT}/target/coverage"
BENCHMARK_DIR="${PROJECT_ROOT}/target/benchmarks"

# Funzioni di utilità
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Crea directory per i report
setup_directories() {
    log_info "Setting up test directories..."
    mkdir -p "${REPORTS_DIR}"
    mkdir -p "${COVERAGE_DIR}"
    mkdir -p "${BENCHMARK_DIR}"
}

# Esegue unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    cd "${PROJECT_ROOT}"
    
    # Test con output dettagliato
    if cargo test --lib --verbose -- --nocapture --test-threads=1 2>&1 | tee "${REPORTS_DIR}/unit-tests.log"; then
        log_success "Unit tests passed"
        return 0
    else
        log_error "Unit tests failed"
        return 1
    fi
}

# Esegue integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    cd "${PROJECT_ROOT}"
    
    if cargo test --test integration_test --verbose -- --nocapture 2>&1 | tee "${REPORTS_DIR}/integration-tests.log"; then
        log_success "Integration tests passed"
        return 0
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# Esegue benchmarks
run_benchmarks() {
    log_info "Running performance benchmarks..."
    
    cd "${PROJECT_ROOT}"
    
    # Benchmark con divan
    if cargo bench --bench ui_performance 2>&1 | tee "${REPORTS_DIR}/ui-benchmarks.log"; then
        log_success "UI benchmarks completed"
    else
        log_warning "UI benchmarks failed"
    fi
    
    if cargo bench --bench theme_performance 2>&1 | tee "${REPORTS_DIR}/theme-benchmarks.log"; then
        log_success "Theme benchmarks completed"
    else
        log_warning "Theme benchmarks failed"
    fi
    
    if cargo bench --bench component_performance 2>&1 | tee "${REPORTS_DIR}/component-benchmarks.log"; then
        log_success "Component benchmarks completed"
    else
        log_warning "Component benchmarks failed"
    fi
}

# Esegue test di performance
run_performance_tests() {
    log_info "Running performance tests..."
    
    cd "${PROJECT_ROOT}"
    
    # Test di performance specifici
    if cargo test --release performance -- --nocapture 2>&1 | tee "${REPORTS_DIR}/performance-tests.log"; then
        log_success "Performance tests passed"
        return 0
    else
        log_warning "Performance tests had issues"
        return 1
    fi
}

# Genera report di coverage
generate_coverage_report() {
    log_info "Generating code coverage report..."
    
    cd "${PROJECT_ROOT}"
    
    # Installa tarpaulin se non presente
    if ! command -v cargo-tarpaulin &> /dev/null; then
        log_info "Installing cargo-tarpaulin..."
        cargo install cargo-tarpaulin
    fi
    
    # Genera coverage report
    if cargo tarpaulin --out Html --output-dir "${COVERAGE_DIR}" --timeout 300 2>&1 | tee "${REPORTS_DIR}/coverage.log"; then
        log_success "Coverage report generated in ${COVERAGE_DIR}"
        return 0
    else
        log_warning "Coverage report generation failed"
        return 1
    fi
}

# Esegue clippy per quality checks
run_quality_checks() {
    log_info "Running code quality checks..."
    
    cd "${PROJECT_ROOT}"
    
    # Clippy
    if cargo clippy --all-targets --all-features -- -D warnings 2>&1 | tee "${REPORTS_DIR}/clippy.log"; then
        log_success "Clippy checks passed"
    else
        log_warning "Clippy found issues"
    fi
    
    # Formatting check
    if cargo fmt -- --check 2>&1 | tee "${REPORTS_DIR}/fmt.log"; then
        log_success "Code formatting is correct"
    else
        log_warning "Code formatting issues found"
    fi
}

# Esegue test di sicurezza
run_security_tests() {
    log_info "Running security tests..."
    
    cd "${PROJECT_ROOT}"
    
    # Installa cargo-audit se non presente
    if ! command -v cargo-audit &> /dev/null; then
        log_info "Installing cargo-audit..."
        cargo install cargo-audit
    fi
    
    # Audit delle dipendenze
    if cargo audit 2>&1 | tee "${REPORTS_DIR}/security-audit.log"; then
        log_success "Security audit passed"
        return 0
    else
        log_warning "Security audit found issues"
        return 1
    fi
}

# Genera report finale
generate_final_report() {
    log_info "Generating final test report..."
    
    local report_file="${REPORTS_DIR}/final-report.md"
    
    cat > "${report_file}" << EOF
# MATRIX IDE Test Report

Generated on: $(date)

## Test Results Summary

### Unit Tests
$(if [ -f "${REPORTS_DIR}/unit-tests.log" ]; then echo "✅ Completed"; else echo "❌ Failed"; fi)

### Integration Tests
$(if [ -f "${REPORTS_DIR}/integration-tests.log" ]; then echo "✅ Completed"; else echo "❌ Failed"; fi)

### Performance Tests
$(if [ -f "${REPORTS_DIR}/performance-tests.log" ]; then echo "✅ Completed"; else echo "❌ Failed"; fi)

### Benchmarks
$(if [ -f "${REPORTS_DIR}/ui-benchmarks.log" ]; then echo "✅ UI Benchmarks Completed"; else echo "❌ UI Benchmarks Failed"; fi)
$(if [ -f "${REPORTS_DIR}/theme-benchmarks.log" ]; then echo "✅ Theme Benchmarks Completed"; else echo "❌ Theme Benchmarks Failed"; fi)
$(if [ -f "${REPORTS_DIR}/component-benchmarks.log" ]; then echo "✅ Component Benchmarks Completed"; else echo "❌ Component Benchmarks Failed"; fi)

### Code Quality
$(if [ -f "${REPORTS_DIR}/clippy.log" ]; then echo "✅ Clippy Checks Completed"; else echo "❌ Clippy Checks Failed"; fi)
$(if [ -f "${REPORTS_DIR}/fmt.log" ]; then echo "✅ Format Checks Completed"; else echo "❌ Format Checks Failed"; fi)

### Security
$(if [ -f "${REPORTS_DIR}/security-audit.log" ]; then echo "✅ Security Audit Completed"; else echo "❌ Security Audit Failed"; fi)

### Coverage
$(if [ -f "${COVERAGE_DIR}/tarpaulin-report.html" ]; then echo "✅ Coverage Report Generated"; else echo "❌ Coverage Report Failed"; fi)

## Files Generated

- Unit Tests Log: \`${REPORTS_DIR}/unit-tests.log\`
- Integration Tests Log: \`${REPORTS_DIR}/integration-tests.log\`
- Performance Tests Log: \`${REPORTS_DIR}/performance-tests.log\`
- Benchmarks: \`${BENCHMARK_DIR}/\`
- Coverage Report: \`${COVERAGE_DIR}/tarpaulin-report.html\`
- Quality Checks: \`${REPORTS_DIR}/clippy.log\`
- Security Audit: \`${REPORTS_DIR}/security-audit.log\`

EOF

    log_success "Final report generated: ${report_file}"
}

# Funzione principale
main() {
    log_info "Starting MATRIX IDE test automation..."
    
    setup_directories
    
    local exit_code=0
    
    # Esegui tutti i test
    run_unit_tests || exit_code=1
    run_integration_tests || exit_code=1
    run_performance_tests || exit_code=1
    run_benchmarks || exit_code=1
    run_quality_checks || exit_code=1
    run_security_tests || exit_code=1
    generate_coverage_report || exit_code=1
    
    generate_final_report
    
    if [ $exit_code -eq 0 ]; then
        log_success "All tests completed successfully!"
    else
        log_warning "Some tests had issues. Check the reports for details."
    fi
    
    return $exit_code
}

# Gestione argomenti
case "${1:-all}" in
    "unit")
        setup_directories
        run_unit_tests
        ;;
    "integration")
        setup_directories
        run_integration_tests
        ;;
    "performance")
        setup_directories
        run_performance_tests
        ;;
    "benchmarks")
        setup_directories
        run_benchmarks
        ;;
    "quality")
        setup_directories
        run_quality_checks
        ;;
    "security")
        setup_directories
        run_security_tests
        ;;
    "coverage")
        setup_directories
        generate_coverage_report
        ;;
    "all")
        main
        ;;
    *)
        echo "Usage: $0 [unit|integration|performance|benchmarks|quality|security|coverage|all]"
        exit 1
        ;;
esac
