//! Render Cache Module
//!
//! Questo modulo implementa un sistema di cache per il rendering
//! per migliorare le performance evitando ridisegni non necessari.

use super::{PerformanceResult, PerformanceError};
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use std::hash::{Hash, Hasher};
use serde::{Serialize, Deserialize};

/// Cache per il rendering
#[derive(Debug)]
pub struct RenderCache {
    /// Entries della cache
    entries: Arc<RwLock<HashMap<CacheKey, CacheEntry>>>,
    /// Configurazione
    config: CacheConfig,
    /// Statistiche
    stats: Arc<RwLock<CacheStats>>,
}

/// Chiave per la cache
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct CacheKey {
    /// ID del componente
    pub component_id: String,
    /// Hash del contenuto
    pub content_hash: u64,
    /// Dimensioni del rendering
    pub dimensions: (u32, u32),
    /// Versione del tema
    pub theme_version: u32,
}

/// Entry della cache
#[derive(Debug, Clone)]
struct CacheEntry {
    /// Dati renderizzati (simulati come Vec<u8>)
    pub data: Vec<u8>,
    /// Timestamp di creazione
    pub created_at: std::time::Instant,
    /// Timestamp ultimo accesso
    pub last_accessed: std::time::Instant,
    /// Numero di accessi
    pub access_count: u64,
    /// Dimensione in bytes
    pub size_bytes: usize,
}

/// Configurazione della cache
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// Dimensione massima della cache (entries)
    pub max_entries: usize,
    /// Dimensione massima in bytes
    pub max_size_bytes: usize,
    /// TTL per le entries (secondi)
    pub ttl_seconds: u64,
    /// Strategia di eviction
    pub eviction_strategy: EvictionStrategy,
    /// Abilita compressione
    pub enable_compression: bool,
}

/// Strategia di eviction
#[derive(Debug, Clone)]
pub enum EvictionStrategy {
    /// Least Recently Used
    LRU,
    /// Least Frequently Used
    LFU,
    /// First In First Out
    FIFO,
    /// Random
    Random,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 1000,
            max_size_bytes: 100 * 1024 * 1024, // 100MB
            ttl_seconds: 300, // 5 minuti
            eviction_strategy: EvictionStrategy::LRU,
            enable_compression: true,
        }
    }
}

/// Statistiche della cache
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// Numero di hit
    pub hits: u64,
    /// Numero di miss
    pub misses: u64,
    /// Numero di entries correnti
    pub current_entries: usize,
    /// Dimensione corrente in bytes
    pub current_size_bytes: usize,
    /// Numero di evictions
    pub evictions: u64,
    /// Tempo medio di accesso (microseconds)
    pub avg_access_time_us: f64,
    /// Hit rate (%)
    pub hit_rate_percent: f64,
}

impl Default for CacheStats {
    fn default() -> Self {
        Self {
            hits: 0,
            misses: 0,
            current_entries: 0,
            current_size_bytes: 0,
            evictions: 0,
            avg_access_time_us: 0.0,
            hit_rate_percent: 0.0,
        }
    }
}

impl RenderCache {
    /// Crea una nuova cache
    pub fn new(max_entries: usize) -> PerformanceResult<Self> {
        let config = CacheConfig {
            max_entries,
            ..Default::default()
        };
        Self::with_config(config)
    }
    
    /// Crea una nuova cache con configurazione personalizzata
    pub fn with_config(config: CacheConfig) -> PerformanceResult<Self> {
        Ok(Self {
            entries: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
        })
    }
    
    /// Ottiene un elemento dalla cache
    pub fn get(&self, key: &CacheKey) -> Option<Vec<u8>> {
        let start_time = std::time::Instant::now();
        
        let mut entries = self.entries.write().ok()?;
        let mut stats = self.stats.write().ok()?;
        
        if let Some(entry) = entries.get_mut(key) {
            // Controlla TTL
            if entry.created_at.elapsed().as_secs() > self.config.ttl_seconds {
                entries.remove(key);
                stats.misses += 1;
                return None;
            }
            
            // Aggiorna statistiche di accesso
            entry.last_accessed = std::time::Instant::now();
            entry.access_count += 1;
            
            stats.hits += 1;
            
            let access_time = start_time.elapsed().as_micros() as f64;
            stats.avg_access_time_us = (stats.avg_access_time_us + access_time) / 2.0;
            
            Some(entry.data.clone())
        } else {
            stats.misses += 1;
            None
        }
    }
    
    /// Inserisce un elemento nella cache
    pub fn put(&self, key: CacheKey, data: Vec<u8>) -> PerformanceResult<()> {
        let mut entries = self.entries.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock entries: {}", e)))?;
        
        let mut stats = self.stats.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock stats: {}", e)))?;
        
        let data_size = data.len();
        
        // Comprimi i dati se abilitato
        let final_data = if self.config.enable_compression {
            self.compress_data(&data)?
        } else {
            data
        };
        
        // Controlla se è necessario fare eviction
        while entries.len() >= self.config.max_entries || 
              stats.current_size_bytes + final_data.len() > self.config.max_size_bytes {
            self.evict_entry(&mut entries, &mut stats)?;
        }
        
        // Inserisce la nuova entry
        let entry = CacheEntry {
            data: final_data.clone(),
            created_at: std::time::Instant::now(),
            last_accessed: std::time::Instant::now(),
            access_count: 0,
            size_bytes: final_data.len(),
        };
        
        entries.insert(key, entry);
        stats.current_entries = entries.len();
        stats.current_size_bytes += final_data.len();
        
        // Aggiorna hit rate
        let total_requests = stats.hits + stats.misses;
        if total_requests > 0 {
            stats.hit_rate_percent = (stats.hits as f64 / total_requests as f64) * 100.0;
        }
        
        Ok(())
    }
    
    /// Rimuove un elemento dalla cache
    pub fn remove(&self, key: &CacheKey) -> PerformanceResult<bool> {
        let mut entries = self.entries.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock entries: {}", e)))?;
        
        let mut stats = self.stats.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock stats: {}", e)))?;
        
        if let Some(entry) = entries.remove(key) {
            stats.current_entries = entries.len();
            stats.current_size_bytes = stats.current_size_bytes.saturating_sub(entry.size_bytes);
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    /// Pulisce tutta la cache
    pub async fn clear(&self) -> PerformanceResult<()> {
        let mut entries = self.entries.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock entries: {}", e)))?;
        
        let mut stats = self.stats.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock stats: {}", e)))?;
        
        entries.clear();
        stats.current_entries = 0;
        stats.current_size_bytes = 0;
        
        Ok(())
    }
    
    /// Pulisce le entries scadute
    pub fn cleanup_expired(&self) -> PerformanceResult<usize> {
        let mut entries = self.entries.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock entries: {}", e)))?;
        
        let mut stats = self.stats.write()
            .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock stats: {}", e)))?;
        
        let mut removed_count = 0;
        let mut removed_size = 0;
        
        entries.retain(|_, entry| {
            if entry.created_at.elapsed().as_secs() > self.config.ttl_seconds {
                removed_count += 1;
                removed_size += entry.size_bytes;
                false
            } else {
                true
            }
        });
        
        stats.current_entries = entries.len();
        stats.current_size_bytes = stats.current_size_bytes.saturating_sub(removed_size);
        
        Ok(removed_count)
    }
    
    /// Esegue eviction di una entry
    fn evict_entry(&self, entries: &mut HashMap<CacheKey, CacheEntry>, stats: &mut CacheStats) -> PerformanceResult<()> {
        if entries.is_empty() {
            return Ok(());
        }
        
        let key_to_remove = match self.config.eviction_strategy {
            EvictionStrategy::LRU => {
                entries.iter()
                    .min_by_key(|(_, entry)| entry.last_accessed)
                    .map(|(key, _)| key.clone())
            }
            EvictionStrategy::LFU => {
                entries.iter()
                    .min_by_key(|(_, entry)| entry.access_count)
                    .map(|(key, _)| key.clone())
            }
            EvictionStrategy::FIFO => {
                entries.iter()
                    .min_by_key(|(_, entry)| entry.created_at)
                    .map(|(key, _)| key.clone())
            }
            EvictionStrategy::Random => {
                use std::collections::hash_map::RandomState;
                use std::hash::BuildHasher;
                
                let hasher = RandomState::new().build_hasher();
                let mut hasher_clone = hasher;
                hasher_clone.write_u64(std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_nanos() as u64);
                let random_index = hasher_clone.finish() as usize % entries.len();
                
                entries.keys().nth(random_index).cloned()
            }
        };
        
        if let Some(key) = key_to_remove {
            if let Some(entry) = entries.remove(&key) {
                stats.current_entries = entries.len();
                stats.current_size_bytes = stats.current_size_bytes.saturating_sub(entry.size_bytes);
                stats.evictions += 1;
            }
        }
        
        Ok(())
    }
    
    /// Comprimi i dati
    fn compress_data(&self, data: &[u8]) -> PerformanceResult<Vec<u8>> {
        // Implementazione semplificata - in un'implementazione reale
        // userebbe algoritmi di compressione come LZ4 o Zstd
        
        // Simula compressione riducendo la dimensione del 30%
        let compressed_size = (data.len() as f64 * 0.7) as usize;
        let mut compressed = vec![0u8; compressed_size];
        
        // Copia una parte dei dati originali (simulazione)
        let copy_size = compressed_size.min(data.len());
        compressed[..copy_size].copy_from_slice(&data[..copy_size]);
        
        Ok(compressed)
    }
    
    /// Ottiene le statistiche correnti
    pub fn get_stats(&self) -> CacheStats {
        self.stats.read().unwrap().clone()
    }
    
    /// Reset delle statistiche
    pub fn reset_stats(&self) -> PerformanceResult<()> {
        if let Ok(mut stats) = self.stats.write() {
            *stats = CacheStats::default();
        }
        Ok(())
    }
    
    /// Ottimizza la cache
    pub fn optimize(&self) -> PerformanceResult<()> {
        // Pulisce entries scadute
        self.cleanup_expired()?;
        
        // Esegue compattazione se necessario
        let stats = self.get_stats();
        if stats.current_entries > self.config.max_entries / 2 {
            // Rimuove entries meno utilizzate
            let mut entries = self.entries.write()
                .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock entries: {}", e)))?;
            
            let mut stats = self.stats.write()
                .map_err(|e| PerformanceError::CacheOperationFailed(format!("Failed to lock stats: {}", e)))?;
            
            // Rimuove il 25% delle entries meno utilizzate
            let target_removals = entries.len() / 4;
            for _ in 0..target_removals {
                self.evict_entry(&mut entries, &mut stats)?;
            }
        }
        
        Ok(())
    }
}

impl CacheKey {
    /// Crea una nuova chiave di cache
    pub fn new(component_id: String, content: &[u8], dimensions: (u32, u32), theme_version: u32) -> Self {
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        content.hash(&mut hasher);
        let content_hash = hasher.finish();
        
        Self {
            component_id,
            content_hash,
            dimensions,
            theme_version,
        }
    }
}
