//! Performance Optimization Module
//!
//! Questo modulo fornisce strumenti per l'ottimizzazione delle performance
//! dell'interfaccia utente di MATRIX IDE.

pub mod profiler;
pub mod optimizer;
pub mod memory_manager;
pub mod render_cache;
pub mod async_scheduler;

use std::sync::Arc;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};

/// Errori del sistema di performance
#[derive(Debug, thiserror::Error)]
pub enum PerformanceError {
    #[error("Profiling failed: {0}")]
    ProfilingFailed(String),
    
    #[error("Optimization failed: {0}")]
    OptimizationFailed(String),
    
    #[error("Memory allocation failed: {0}")]
    MemoryAllocationFailed(String),
    
    #[error("Cache operation failed: {0}")]
    CacheOperationFailed(String),
    
    #[error("Scheduler error: {0}")]
    SchedulerError(String),
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

/// Risultato delle operazioni di performance
pub type PerformanceResult<T> = Result<T, PerformanceError>;

/// Metriche di performance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Timestamp della misurazione
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Tempo di rendering frame (ms)
    pub frame_time: f64,
    /// FPS (frames per second)
    pub fps: f64,
    /// Utilizzo memoria (bytes)
    pub memory_usage: u64,
    /// Numero di draw calls
    pub draw_calls: u32,
    /// Tempo di layout (ms)
    pub layout_time: f64,
    /// Tempo di paint (ms)
    pub paint_time: f64,
    /// Numero di componenti renderizzati
    pub component_count: u32,
    /// Cache hit rate (%)
    pub cache_hit_rate: f64,
}

impl PerformanceMetrics {
    /// Crea nuove metriche vuote
    pub fn new() -> Self {
        Self {
            timestamp: chrono::Utc::now(),
            frame_time: 0.0,
            fps: 0.0,
            memory_usage: 0,
            draw_calls: 0,
            layout_time: 0.0,
            paint_time: 0.0,
            component_count: 0,
            cache_hit_rate: 0.0,
        }
    }
    
    /// Calcola il performance score (0-100)
    pub fn calculate_score(&self) -> f64 {
        let mut score = 100.0;
        
        // Penalizza FPS bassi
        if self.fps < 60.0 {
            score -= (60.0 - self.fps) * 2.0;
        }
        
        // Penalizza frame time alti
        if self.frame_time > 16.67 {
            score -= (self.frame_time - 16.67) * 3.0;
        }
        
        // Penalizza utilizzo memoria alto
        let memory_mb = self.memory_usage as f64 / (1024.0 * 1024.0);
        if memory_mb > 100.0 {
            score -= (memory_mb - 100.0) * 0.5;
        }
        
        // Bonus per cache hit rate alto
        score += self.cache_hit_rate * 0.2;
        
        score.max(0.0).min(100.0)
    }
    
    /// Verifica se le performance sono accettabili
    pub fn is_acceptable(&self) -> bool {
        self.fps >= 30.0 && 
        self.frame_time <= 33.33 && 
        self.memory_usage < 500 * 1024 * 1024 // 500MB
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// Manager per le performance
pub struct PerformanceManager {
    /// Profiler per le metriche
    profiler: Arc<profiler::Profiler>,
    /// Optimizer per le ottimizzazioni
    optimizer: Arc<optimizer::Optimizer>,
    /// Memory manager
    memory_manager: Arc<memory_manager::MemoryManager>,
    /// Render cache
    render_cache: Arc<render_cache::RenderCache>,
    /// Async scheduler
    async_scheduler: Arc<async_scheduler::AsyncScheduler>,
    /// Configurazione
    config: PerformanceConfig,
    /// Metriche correnti
    current_metrics: Arc<std::sync::RwLock<PerformanceMetrics>>,
}

/// Configurazione del performance manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Target FPS
    pub target_fps: f64,
    /// Limite memoria massima (MB)
    pub max_memory_mb: u64,
    /// Abilita profiling automatico
    pub enable_auto_profiling: bool,
    /// Intervallo di profiling (ms)
    pub profiling_interval_ms: u64,
    /// Abilita ottimizzazioni automatiche
    pub enable_auto_optimization: bool,
    /// Soglia per ottimizzazioni automatiche
    pub optimization_threshold: f64,
    /// Dimensione cache rendering
    pub render_cache_size: usize,
    /// Numero massimo di task asincroni
    pub max_async_tasks: usize,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            target_fps: 60.0,
            max_memory_mb: 512,
            enable_auto_profiling: true,
            profiling_interval_ms: 1000,
            enable_auto_optimization: true,
            optimization_threshold: 70.0,
            render_cache_size: 1000,
            max_async_tasks: 100,
        }
    }
}

impl PerformanceManager {
    /// Crea un nuovo performance manager
    pub fn new(config: PerformanceConfig) -> PerformanceResult<Self> {
        let profiler = Arc::new(profiler::Profiler::new()?);
        let optimizer = Arc::new(optimizer::Optimizer::new()?);
        let memory_manager = Arc::new(memory_manager::MemoryManager::new(config.max_memory_mb)?);
        let render_cache = Arc::new(render_cache::RenderCache::new(config.render_cache_size)?);
        let async_scheduler = Arc::new(async_scheduler::AsyncScheduler::new(config.max_async_tasks)?);
        
        Ok(Self {
            profiler,
            optimizer,
            memory_manager,
            render_cache,
            async_scheduler,
            config,
            current_metrics: Arc::new(std::sync::RwLock::new(PerformanceMetrics::new())),
        })
    }
    
    /// Crea un performance manager con configurazione di default
    pub fn with_default_config() -> PerformanceResult<Self> {
        Self::new(PerformanceConfig::default())
    }
    
    /// Avvia il monitoraggio delle performance
    pub async fn start_monitoring(&self) -> PerformanceResult<()> {
        if self.config.enable_auto_profiling {
            self.start_auto_profiling().await?;
        }
        
        if self.config.enable_auto_optimization {
            self.start_auto_optimization().await?;
        }
        
        Ok(())
    }
    
    /// Avvia il profiling automatico
    async fn start_auto_profiling(&self) -> PerformanceResult<()> {
        let profiler = Arc::clone(&self.profiler);
        let current_metrics = Arc::clone(&self.current_metrics);
        let interval = Duration::from_millis(self.config.profiling_interval_ms);
        
        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                if let Ok(metrics) = profiler.collect_metrics().await {
                    if let Ok(mut current) = current_metrics.write() {
                        *current = metrics;
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Avvia l'ottimizzazione automatica
    async fn start_auto_optimization(&self) -> PerformanceResult<()> {
        let optimizer = Arc::clone(&self.optimizer);
        let current_metrics = Arc::clone(&self.current_metrics);
        let threshold = self.config.optimization_threshold;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            
            loop {
                interval.tick().await;
                
                // Clone metrics to avoid holding lock across await
                let metrics_clone = if let Ok(metrics) = current_metrics.read() {
                    let score = metrics.calculate_score();
                    if score < threshold {
                        Some(metrics.clone())
                    } else {
                        None
                    }
                } else {
                    None
                };

                // Now we can safely await without holding the lock
                if let Some(metrics) = metrics_clone {
                    if let Err(e) = optimizer.optimize_performance(&metrics).await {
                        eprintln!("Auto-optimization failed: {}", e);
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Ottiene le metriche correnti
    pub fn get_current_metrics(&self) -> PerformanceMetrics {
        self.current_metrics.read().unwrap().clone()
    }
    
    /// Forza la raccolta di metriche
    pub async fn collect_metrics(&self) -> PerformanceResult<PerformanceMetrics> {
        self.profiler.collect_metrics().await
    }
    
    /// Esegue ottimizzazioni manuali
    pub async fn optimize(&self) -> PerformanceResult<()> {
        let metrics = self.get_current_metrics();
        let _results = self.optimizer.optimize_performance(&metrics).await?;
        Ok(())
    }
    
    /// Pulisce la cache di rendering
    pub async fn clear_render_cache(&self) -> PerformanceResult<()> {
        self.render_cache.clear().await
    }
    
    /// Ottiene statistiche della cache
    pub fn get_cache_stats(&self) -> render_cache::CacheStats {
        self.render_cache.get_stats()
    }
    
    /// Ottiene statistiche del memory manager
    pub fn get_memory_stats(&self) -> memory_manager::MemoryStats {
        self.memory_manager.get_stats()
    }
    
    /// Ottiene statistiche dell'async scheduler
    pub fn get_scheduler_stats(&self) -> async_scheduler::SchedulerStats {
        self.async_scheduler.get_stats()
    }
    
    /// Genera un report di performance dettagliato
    pub async fn generate_performance_report(&self) -> PerformanceResult<PerformanceReport> {
        let metrics = self.collect_metrics().await?;
        let cache_stats = self.get_cache_stats();
        let memory_stats = self.get_memory_stats();
        let scheduler_stats = self.get_scheduler_stats();
        
        Ok(PerformanceReport {
            timestamp: chrono::Utc::now(),
            metrics,
            cache_stats,
            memory_stats,
            scheduler_stats,
            recommendations: self.generate_recommendations().await?,
        })
    }
    
    /// Genera raccomandazioni per migliorare le performance
    async fn generate_recommendations(&self) -> PerformanceResult<Vec<String>> {
        let metrics = self.get_current_metrics();
        let mut recommendations = Vec::new();
        
        if metrics.fps < self.config.target_fps {
            recommendations.push(format!(
                "FPS sotto target: {:.1} < {:.1}. Considerare riduzione qualità rendering.",
                metrics.fps, self.config.target_fps
            ));
        }
        
        if metrics.frame_time > 16.67 {
            recommendations.push(format!(
                "Frame time alto: {:.2}ms. Ottimizzare algoritmi di rendering.",
                metrics.frame_time
            ));
        }
        
        let memory_mb = metrics.memory_usage as f64 / (1024.0 * 1024.0);
        if memory_mb > self.config.max_memory_mb as f64 * 0.8 {
            recommendations.push(format!(
                "Utilizzo memoria elevato: {:.1}MB. Implementare garbage collection.",
                memory_mb
            ));
        }
        
        if metrics.cache_hit_rate < 80.0 {
            recommendations.push(format!(
                "Cache hit rate basso: {:.1}%. Ottimizzare strategia di caching.",
                metrics.cache_hit_rate
            ));
        }
        
        if metrics.draw_calls > 1000 {
            recommendations.push(format!(
                "Troppi draw calls: {}. Implementare batching.",
                metrics.draw_calls
            ));
        }
        
        if recommendations.is_empty() {
            recommendations.push("Performance ottimali! 🚀".to_string());
        }
        
        Ok(recommendations)
    }
}

/// Report di performance dettagliato
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceReport {
    /// Timestamp del report
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Metriche di performance
    pub metrics: PerformanceMetrics,
    /// Statistiche cache
    pub cache_stats: render_cache::CacheStats,
    /// Statistiche memoria
    pub memory_stats: memory_manager::MemoryStats,
    /// Statistiche scheduler
    pub scheduler_stats: async_scheduler::SchedulerStats,
    /// Raccomandazioni
    pub recommendations: Vec<String>,
}
