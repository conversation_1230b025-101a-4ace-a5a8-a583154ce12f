//! Memory Manager Module
//!
//! Questo modulo gestisce l'allocazione e deallocazione della memoria
//! per ottimizzare le performance dell'interfaccia utente.

use super::{PerformanceResult, PerformanceError};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// Memory manager per l'ottimizzazione della memoria
#[derive(Debug)]
pub struct MemoryManager {
    /// Pool di memoria per allocazioni frequenti
    memory_pools: Arc<Mutex<HashMap<usize, MemoryPool>>>,
    /// Statistiche di utilizzo
    stats: Arc<Mutex<MemoryStats>>,
    /// Configurazione
    config: MemoryConfig,
}

/// Pool di memoria per allocazioni di dimensione fissa
#[derive(Debug)]
struct MemoryPool {
    /// Dimensione dei blocchi in questo pool
    block_size: usize,
    /// Blocchi disponibili
    available_blocks: Vec<*mut u8>,
    /// Blocchi allocati
    allocated_blocks: Vec<*mut u8>,
    /// Capacità massima del pool
    max_capacity: usize,
}

/// Configurazione del memory manager
#[derive(Debug, Clone)]
pub struct MemoryConfig {
    /// Limite massimo di memoria (MB)
    pub max_memory_mb: u64,
    /// Abilita garbage collection automatico
    pub enable_auto_gc: bool,
    /// Soglia per garbage collection (% di memoria utilizzata)
    pub gc_threshold_percent: f64,
    /// Dimensioni dei pool di memoria
    pub pool_sizes: Vec<usize>,
    /// Capacità iniziale dei pool
    pub initial_pool_capacity: usize,
}

impl Default for MemoryConfig {
    fn default() -> Self {
        Self {
            max_memory_mb: 512,
            enable_auto_gc: true,
            gc_threshold_percent: 80.0,
            pool_sizes: vec![64, 128, 256, 512, 1024, 2048, 4096],
            initial_pool_capacity: 100,
        }
    }
}

/// Statistiche del memory manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryStats {
    /// Memoria totale allocata (bytes)
    pub total_allocated: u64,
    /// Memoria attualmente in uso (bytes)
    pub current_usage: u64,
    /// Numero di allocazioni
    pub allocation_count: u64,
    /// Numero di deallocazioni
    pub deallocation_count: u64,
    /// Numero di garbage collection eseguite
    pub gc_count: u64,
    /// Memoria liberata da garbage collection (bytes)
    pub gc_freed_memory: u64,
    /// Frammentazione della memoria (%)
    pub fragmentation_percent: f64,
    /// Efficienza dei pool (%)
    pub pool_efficiency_percent: f64,
}

impl Default for MemoryStats {
    fn default() -> Self {
        Self {
            total_allocated: 0,
            current_usage: 0,
            allocation_count: 0,
            deallocation_count: 0,
            gc_count: 0,
            gc_freed_memory: 0,
            fragmentation_percent: 0.0,
            pool_efficiency_percent: 100.0,
        }
    }
}

impl MemoryManager {
    /// Crea un nuovo memory manager
    pub fn new(max_memory_mb: u64) -> PerformanceResult<Self> {
        let config = MemoryConfig {
            max_memory_mb,
            ..Default::default()
        };
        Self::with_config(config)
    }
    
    /// Crea un nuovo memory manager con configurazione personalizzata
    pub fn with_config(config: MemoryConfig) -> PerformanceResult<Self> {
        let mut memory_pools = HashMap::new();
        
        // Inizializza i pool di memoria
        for &size in &config.pool_sizes {
            memory_pools.insert(size, MemoryPool::new(size, config.initial_pool_capacity)?);
        }
        
        Ok(Self {
            memory_pools: Arc::new(Mutex::new(memory_pools)),
            stats: Arc::new(Mutex::new(MemoryStats::default())),
            config,
        })
    }
    
    /// Alloca memoria dal pool appropriato
    pub fn allocate(&self, size: usize) -> PerformanceResult<*mut u8> {
        // Trova il pool più piccolo che può contenere la richiesta
        let pool_size = self.config.pool_sizes.iter()
            .find(|&&pool_size| pool_size >= size)
            .copied()
            .unwrap_or_else(|| {
                // Se nessun pool è abbastanza grande, usa il più grande
                *self.config.pool_sizes.last().unwrap_or(&4096)
            });
        
        let mut pools = self.memory_pools.lock()
            .map_err(|e| PerformanceError::MemoryAllocationFailed(format!("Failed to lock pools: {}", e)))?;
        
        let pool = pools.get_mut(&pool_size)
            .ok_or_else(|| PerformanceError::MemoryAllocationFailed(format!("Pool size {} not found", pool_size)))?;
        
        let ptr = pool.allocate()?;
        
        // Aggiorna statistiche
        if let Ok(mut stats) = self.stats.lock() {
            stats.total_allocated += pool_size as u64;
            stats.current_usage += pool_size as u64;
            stats.allocation_count += 1;
        }
        
        // Controlla se è necessario garbage collection
        if self.config.enable_auto_gc {
            self.check_gc_threshold()?;
        }
        
        Ok(ptr)
    }
    
    /// Dealloca memoria restituendola al pool
    pub fn deallocate(&self, ptr: *mut u8, size: usize) -> PerformanceResult<()> {
        let pool_size = self.config.pool_sizes.iter()
            .find(|&&pool_size| pool_size >= size)
            .copied()
            .unwrap_or_else(|| *self.config.pool_sizes.last().unwrap_or(&4096));
        
        let mut pools = self.memory_pools.lock()
            .map_err(|e| PerformanceError::MemoryAllocationFailed(format!("Failed to lock pools: {}", e)))?;
        
        let pool = pools.get_mut(&pool_size)
            .ok_or_else(|| PerformanceError::MemoryAllocationFailed(format!("Pool size {} not found", pool_size)))?;
        
        pool.deallocate(ptr)?;
        
        // Aggiorna statistiche
        if let Ok(mut stats) = self.stats.lock() {
            stats.current_usage = stats.current_usage.saturating_sub(pool_size as u64);
            stats.deallocation_count += 1;
        }
        
        Ok(())
    }
    
    /// Esegue garbage collection
    pub fn garbage_collect(&self) -> PerformanceResult<u64> {
        let mut freed_memory = 0u64;
        
        // Simula garbage collection
        // In un'implementazione reale, identificherebbe e libererebbe memoria non utilizzata
        
        if let Ok(mut stats) = self.stats.lock() {
            // Simula liberazione del 10% della memoria corrente
            let to_free = (stats.current_usage as f64 * 0.1) as u64;
            stats.current_usage = stats.current_usage.saturating_sub(to_free);
            stats.gc_freed_memory += to_free;
            stats.gc_count += 1;
            freed_memory = to_free;
        }
        
        Ok(freed_memory)
    }
    
    /// Controlla se è necessario eseguire garbage collection
    fn check_gc_threshold(&self) -> PerformanceResult<()> {
        if let Ok(stats) = self.stats.lock() {
            let max_memory_bytes = self.config.max_memory_mb * 1024 * 1024;
            let usage_percent = (stats.current_usage as f64 / max_memory_bytes as f64) * 100.0;
            
            if usage_percent > self.config.gc_threshold_percent {
                drop(stats); // Rilascia il lock prima di chiamare garbage_collect
                self.garbage_collect()?;
            }
        }
        
        Ok(())
    }
    
    /// Ottiene le statistiche correnti
    pub fn get_stats(&self) -> MemoryStats {
        self.stats.lock().unwrap().clone()
    }
    
    /// Reset delle statistiche
    pub fn reset_stats(&self) -> PerformanceResult<()> {
        if let Ok(mut stats) = self.stats.lock() {
            *stats = MemoryStats::default();
        }
        Ok(())
    }
    
    /// Ottimizza i pool di memoria
    pub fn optimize_pools(&self) -> PerformanceResult<()> {
        let mut pools = self.memory_pools.lock()
            .map_err(|e| PerformanceError::OptimizationFailed(format!("Failed to lock pools: {}", e)))?;
        
        for pool in pools.values_mut() {
            pool.optimize()?;
        }
        
        Ok(())
    }
}

impl MemoryPool {
    /// Crea un nuovo pool di memoria
    fn new(block_size: usize, initial_capacity: usize) -> PerformanceResult<Self> {
        let mut pool = Self {
            block_size,
            available_blocks: Vec::with_capacity(initial_capacity),
            allocated_blocks: Vec::new(),
            max_capacity: initial_capacity * 2,
        };
        
        // Pre-alloca alcuni blocchi
        for _ in 0..initial_capacity {
            pool.add_block()?;
        }
        
        Ok(pool)
    }
    
    /// Aggiunge un nuovo blocco al pool
    fn add_block(&mut self) -> PerformanceResult<()> {
        if self.available_blocks.len() + self.allocated_blocks.len() >= self.max_capacity {
            return Err(PerformanceError::MemoryAllocationFailed(
                "Pool capacity exceeded".to_string()
            ));
        }
        
        // Simula allocazione di memoria
        // In un'implementazione reale userebbe allocatori di sistema
        let layout = std::alloc::Layout::from_size_align(self.block_size, 8)
            .map_err(|e| PerformanceError::MemoryAllocationFailed(format!("Invalid layout: {}", e)))?;
        
        let ptr = unsafe { std::alloc::alloc(layout) };
        if ptr.is_null() {
            return Err(PerformanceError::MemoryAllocationFailed(
                "System allocation failed".to_string()
            ));
        }
        
        self.available_blocks.push(ptr);
        Ok(())
    }
    
    /// Alloca un blocco dal pool
    fn allocate(&mut self) -> PerformanceResult<*mut u8> {
        if self.available_blocks.is_empty() {
            self.add_block()?;
        }
        
        let ptr = self.available_blocks.pop()
            .ok_or_else(|| PerformanceError::MemoryAllocationFailed("No blocks available".to_string()))?;
        
        self.allocated_blocks.push(ptr);
        Ok(ptr)
    }
    
    /// Dealloca un blocco restituendolo al pool
    fn deallocate(&mut self, ptr: *mut u8) -> PerformanceResult<()> {
        if let Some(pos) = self.allocated_blocks.iter().position(|&p| p == ptr) {
            self.allocated_blocks.remove(pos);
            self.available_blocks.push(ptr);
            Ok(())
        } else {
            Err(PerformanceError::MemoryAllocationFailed(
                "Pointer not found in allocated blocks".to_string()
            ))
        }
    }
    
    /// Ottimizza il pool
    fn optimize(&mut self) -> PerformanceResult<()> {
        // Rimuove blocchi in eccesso se ce ne sono troppi disponibili
        let target_available = self.allocated_blocks.len().max(10);
        
        while self.available_blocks.len() > target_available {
            if let Some(ptr) = self.available_blocks.pop() {
                unsafe {
                    let layout = std::alloc::Layout::from_size_align(self.block_size, 8)
                        .map_err(|e| PerformanceError::OptimizationFailed(format!("Invalid layout: {}", e)))?;
                    std::alloc::dealloc(ptr, layout);
                }
            }
        }
        
        Ok(())
    }
}

impl Drop for MemoryPool {
    fn drop(&mut self) {
        // Dealloca tutti i blocchi
        let layout = std::alloc::Layout::from_size_align(self.block_size, 8).unwrap();
        
        for ptr in &self.available_blocks {
            unsafe {
                std::alloc::dealloc(*ptr, layout);
            }
        }
        
        for ptr in &self.allocated_blocks {
            unsafe {
                std::alloc::dealloc(*ptr, layout);
            }
        }
    }
}
