//! Widget di ricerca
//!
//! Questo modulo fornisce un widget per la ricerca di file e contenuti nel progetto.

use std::sync::Arc;
use std::path::PathBuf;

use std::thread;

use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use floem::{View, views::{*, Decorators, v_stack_from_iter}};
use floem::views::dropdown::Dropdown;
use floem::peniko::Color;
use floem::event::{Event, EventListener};
use floem::keyboard::{self, Key, Modifiers};

use matrix_core::{Engine as CoreEngine};
use crate::theme::ThemeManager;

/// Modalità di ricerca
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[allow(dead_code)]
enum SearchMode {
    /// Ricerca per file
    FileName,

    /// Ricerca nel contenuto
    Content,
}

/// Risultato della ricerca
#[derive(Debug, <PERSON><PERSON>)]
#[allow(dead_code)]
struct SearchResult {
    /// Percorso del file
    path: PathBuf,

    /// Nome del file
    name: String,

    /// Righe trovate (solo per ricerca contenuto)
    lines: Vec<(usize, String)>,

    /// Query di ricerca
    query: String,
}

/// Stato della ricerca
#[allow(dead_code)]
struct SearchState {
    /// Query di ricerca
    query: RwSignal<String>,

    /// Modalità di ricerca
    mode: RwSignal<SearchMode>,

    /// Risultati della ricerca
    results: RwSignal<Vec<SearchResult>>,

    /// È in corso una ricerca?
    is_searching: RwSignal<bool>,

    /// Directory di ricerca
    search_dir: RwSignal<PathBuf>,
}

/// Widget di ricerca
#[allow(dead_code)]
pub struct Search {
    /// Core Engine
    core: Arc<CoreEngine>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,

    /// Stato della ricerca
    state: SearchState,
}

impl Search {
    /// Crea un nuovo widget di ricerca
    #[allow(dead_code)]
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> impl View {
        let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));

        let state = SearchState {
            query: create_rw_signal(String::new()),
            mode: create_rw_signal(SearchMode::FileName),
            results: create_rw_signal(Vec::new()),
            is_searching: create_rw_signal(false),
            search_dir: create_rw_signal(current_dir),
        };

        let search = Self {
            core: core.clone(),
            theme_manager,
            state,
        };

        let query = search.state.query;
        let mode = search.state.mode;
        let results = search.state.results;
        let is_searching = search.state.is_searching;
        let search_dir = search.state.search_dir;

        let _core_clone = core.clone();

        v_stack((
            h_stack((
                label(move || "Ricerca".to_string())
                    .style(|s| s.font_bold().margin_bottom(8)),

                // Dropdown per la modalità di ricerca
                Dropdown::new(
                    move || {
                        match mode.get() {
                            SearchMode::FileName => "Nome File",
                            SearchMode::Content => "Contenuto",
                        }.to_string()
                    },
                    vec!["Nome File".to_string(), "Contenuto".to_string()],
                )
                .style(|s| {
                    s.margin_left(8)
                     .padding(5)
                     .border_radius(4)
                     .min_width(100.0)
                }),
            ))
            .style(|s| s.width_full().padding(8)),

            h_stack((
                text_input(query)
                    .placeholder("Cerca file o contenuto...")
                    .on_event_stop(EventListener::KeyDown, move |event| {
                        if let Event::KeyDown(key_event) = event {
                            if key_event.key.logical_key == Key::Named(keyboard::NamedKey::Enter) && !query.get().trim().is_empty() {
                                Self::perform_search(
                                    query.get(),
                                    mode.get(),
                                    search_dir.get(),
                                    results,
                                    is_searching,
                                );
                            }
                        }
                    })
                    .style(|s| {
                        s.width_full()
                         .padding(8)
                         .border(1)
                         .border_color(Color::rgb8(0x30, 0x30, 0x30))
                         .border_radius(4)
                         .background(Color::rgb8(0x22, 0x22, 0x22))
                    }),

                button(label(|| "Cerca"))
                    .action(move || {
                        if !query.get().trim().is_empty() {
                            Self::perform_search(
                                query.get(),
                                mode.get(),
                                search_dir.get(),
                                results,
                                is_searching,
                            );
                        }
                    })
                    .style(|s| {
                        s.margin_left(8)
                         .padding(8)
                         .border_radius(4)
                    }),
            ))
            .style(|s| s.width_full().padding_horiz(8)),

            label(move || {
                if is_searching.get() {
                    "Ricerca in corso...".to_string()
                } else if results.get().is_empty() && !query.get().is_empty() {
                    "Nessun risultato trovato".to_string()
                } else if !results.get().is_empty() {
                    format!("{} risultati trovati", results.get().len())
                } else {
                    "".to_string()
                }
            })
            .style(|s| {
                s.width_full()
                 .padding(8)
                 .color(Color::rgb8(0xA0, 0xA0, 0xA0))
                 // .font_style(FontStyle::Italic) // TODO: Fix for Floem 0.2
            }),

            scroll(
                v_stack_from_iter(
                    results.get().into_iter().map(move |result| {
                        label(move || format!("📄 {}: {} results", result.name, result.lines.len()))
                            .style(|s| s.width_full().padding(8))
                    })
                )
                .style(|s| s.gap(4.0).width_full())
            )
            .style(|s| {
                s.width_full()
                 .height_full() // TODO: Replace BoxConstraints/stretch with proper Floem 0.2 equivalent
                 .background(Color::rgb8(0x18, 0x18, 0x18))
                 .padding(8)
                 .border(1)
                 .border_color(Color::rgb8(0x30, 0x30, 0x30))
                 .border_radius(4)
            }),
        ))
        .style(move |s| {
            s.width_full()
             .height_full()
             .background(Color::rgb8(0x21, 0x21, 0x21))
             .color(Color::rgb8(0xE0, 0xE0, 0xE0))
        })
        .keyboard_navigable()
        .on_event(EventListener::KeyDown, move |event| {
            if let Event::KeyDown(key_event) = event {
                if matches!(key_event.key.logical_key, Key::Character(ref c) if c == "f") &&
                   key_event.modifiers.contains(Modifiers::CONTROL) {
                    // Focus sulla casella di ricerca
                    return floem::event::EventPropagation::Stop;
                }
            }
            floem::event::EventPropagation::Continue
        })
    }

    /// Esegue la ricerca
    #[allow(dead_code)]
    fn perform_search(
        query: String,
        mode: SearchMode,
        dir: PathBuf,
        results: RwSignal<Vec<SearchResult>>,
        is_searching: RwSignal<bool>
    ) {
        if query.trim().is_empty() {
            return;
        }

        // Resetta i risultati precedenti
        results.set(Vec::new());
        is_searching.set(true);

        // Clonazione per il thread
        let query_clone = query.clone();
        let dir_clone = dir.clone();

        // Esegue la ricerca in un thread separato
        thread::spawn(move || {
            let search_results = match mode {
                SearchMode::FileName => Self::search_by_filename(&query_clone, &dir_clone),
                SearchMode::Content => Self::search_by_content(&query_clone, &dir_clone),
            };

            // Aggiorna i risultati
            results.set(search_results);
            is_searching.set(false);
        });
    }

    /// Ricerca per nome file
    #[allow(dead_code)]
    fn search_by_filename(query: &str, dir: &PathBuf) -> Vec<SearchResult> {
        let mut results = Vec::new();
        let query_lower = query.to_lowercase();

        // Funzione ricorsiva per cercare nei file
        fn search_dir(
            dir: &PathBuf,
            query: &str,
            query_lower: &str,
            results: &mut Vec<SearchResult>
        ) {
            if let Ok(entries) = std::fs::read_dir(dir) {
                for entry in entries.flatten() {
                    let path = entry.path();

                    // Ottiene il nome del file
                    let name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("")
                        .to_string();

                    // Verifica se corrisponde alla query
                    if name.to_lowercase().contains(query_lower) {
                        results.push(SearchResult {
                            path: path.clone(),
                            name,
                            lines: Vec::new(),
                            query: query.to_string(),
                        });
                    }

                    // Ricorsione per le directory
                    if path.is_dir() {
                        search_dir(&path, query, query_lower, results);
                    }
                }
            }
        }

        search_dir(dir, query, &query_lower, &mut results);

        // Ordina i risultati per nome file
        results.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));

        results
    }

    /// Ricerca nel contenuto dei file
    #[allow(dead_code)]
    fn search_by_content(query: &str, dir: &PathBuf) -> Vec<SearchResult> {
        let mut results = Vec::new();
        let query_lower = query.to_lowercase();

        // Funzione ricorsiva per cercare nei file
        fn search_dir(
            dir: &PathBuf,
            query: &str,
            query_lower: &str,
            results: &mut Vec<SearchResult>
        ) {
            if let Ok(entries) = std::fs::read_dir(dir) {
                for entry in entries.flatten() {
                    let path = entry.path();

                    if path.is_file() {
                        // Ignora file binari o troppo grandi
                        if let Ok(metadata) = std::fs::metadata(&path) {
                            if metadata.len() > 1024 * 1024 * 5 {  // 5MB
                                continue;
                            }
                        }

                        // Legge il contenuto del file
                        if let Ok(content) = std::fs::read_to_string(&path) {
                            let mut matching_lines = Vec::new();

                            // Cerca la query in ogni riga
                            for (i, line) in content.lines().enumerate() {
                                if line.to_lowercase().contains(query_lower) {
                                    matching_lines.push((i + 1, line.to_string()));
                                }
                            }

                            // Se ci sono risultati, aggiunge alla lista
                            if !matching_lines.is_empty() {
                                let name = path.file_name()
                                    .and_then(|n| n.to_str())
                                    .unwrap_or("")
                                    .to_string();

                                results.push(SearchResult {
                                    path: path.clone(),
                                    name,
                                    lines: matching_lines,
                                    query: query.to_string(),
                                });
                            }
                        }
                    } else if path.is_dir() {
                        // Ignora directory nascoste o speciali
                        let dir_name = path.file_name()
                            .and_then(|n| n.to_str())
                            .unwrap_or("");

                        if dir_name.starts_with(".") || dir_name == "node_modules" || dir_name == "target" {
                            continue;
                        }

                        // Ricorsione per le directory
                        search_dir(&path, query, query_lower, results);
                    }
                }
            }
        }

        search_dir(dir, query, &query_lower, &mut results);

        // Ordina i risultati per nome file
        results.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));

        results
    }

    /// Evidenzia la query nel testo
    #[allow(dead_code)]
    fn highlight_query(text: &str, query: &str) -> String {
        if query.is_empty() {
            return text.to_string();
        }

        let query_lower = query.to_lowercase();
        let mut result = String::new();
        let mut last_end = 0;

        // Cerca tutte le occorrenze (case-insensitive)
        let text_lower = text.to_lowercase();
        let mut start = 0;

        while let Some(index) = text_lower[start..].find(&query_lower) {
            let match_start = start + index;
            let match_end = match_start + query.len();

            // Aggiungi il testo prima del match
            result.push_str(&text[last_end..match_start]);

            // Aggiungi il match con l'evidenziazione
            result.push_str("<b>");
            result.push_str(&text[match_start..match_end]);
            result.push_str("</b>");

            last_end = match_end;
            start = match_end;
        }

        // Aggiungi il resto del testo
        result.push_str(&text[last_end..]);

        result
    }
}
