//! Interactive Code Analysis Panel
//!
//! This module provides an interactive panel for code analysis with real-time
//! insights, metrics, and AI-powered suggestions.

use std::sync::Arc;
use std::collections::HashMap;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{container, dyn_container, h_stack, v_stack, v_stack_from_iter, label, button, scroll, Decorators},
    View, IntoView,
    peniko::Color,
};
use serde::{Serialize, Deserialize};

use crate::theme::ThemeManager;
use crate::error::UiError;
use matrix_core::Engine as CoreEngine;

/// Code analysis metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeMetrics {
    pub lines_of_code: usize,
    pub cyclomatic_complexity: f32,
    pub maintainability_index: f32,
    pub technical_debt_ratio: f32,
    pub test_coverage: f32,
    pub duplication_percentage: f32,
}

/// Code quality issue
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CodeIssue {
    pub id: String,
    pub severity: IssueSeverity,
    pub category: IssueCategory,
    pub message: String,
    pub file_path: String,
    pub line_number: usize,
    pub column: usize,
    pub suggestion: Option<String>,
}

/// Issue severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IssueSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

/// Issue categories
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IssueCategory {
    Security,
    Performance,
    Maintainability,
    Reliability,
    Style,
    Documentation,
}

/// Analysis tab types
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum AnalysisTab {
    Overview,
    Issues,
    Metrics,
    Dependencies,
    Suggestions,
}

/// Interactive code analysis panel
pub struct CodeAnalysisPanel {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    
    /// Current active tab
    active_tab: RwSignal<AnalysisTab>,
    
    /// Current file being analyzed
    current_file: RwSignal<Option<String>>,
    
    /// Code metrics
    metrics: RwSignal<Option<CodeMetrics>>,
    
    /// Code issues
    issues: RwSignal<Vec<CodeIssue>>,
    
    /// Analysis status
    is_analyzing: RwSignal<bool>,
    
    /// Analysis progress
    analysis_progress: RwSignal<f32>,
    
    /// Selected issue
    selected_issue: RwSignal<Option<String>>,
    
    /// Filter settings
    severity_filter: RwSignal<Vec<IssueSeverity>>,
    category_filter: RwSignal<Vec<IssueCategory>>,
}

impl CodeAnalysisPanel {
    /// Create a new code analysis panel
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Arc<Self>, UiError> {
        Ok(Arc::new(Self {
            core,
            theme_manager,
            active_tab: create_rw_signal(AnalysisTab::Overview),
            current_file: create_rw_signal(None),
            metrics: create_rw_signal(None),
            issues: create_rw_signal(Vec::new()),
            is_analyzing: create_rw_signal(false),
            analysis_progress: create_rw_signal(0.0),
            selected_issue: create_rw_signal(None),
            severity_filter: create_rw_signal(vec![
                IssueSeverity::Critical,
                IssueSeverity::High,
                IssueSeverity::Medium,
                IssueSeverity::Low,
            ]),
            category_filter: create_rw_signal(vec![
                IssueCategory::Security,
                IssueCategory::Performance,
                IssueCategory::Maintainability,
                IssueCategory::Reliability,
            ]),
        }))
    }

    /// Create the main view
    pub fn create_view(&self) -> Box<dyn View> {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let active_tab = self.active_tab;

        Box::new(container(
            v_stack((
                // Header
                self.create_header(),

                // Tab navigation
                self.create_tab_navigation(),

                // Content area
                container({
                    let view: Box<dyn View> = match active_tab.get() {
                        AnalysisTab::Overview => Box::new(self.create_overview_tab()),
                        AnalysisTab::Issues => Box::new(self.create_issues_tab()),
                        AnalysisTab::Metrics => Box::new(self.create_metrics_tab()),
                        AnalysisTab::Dependencies => Box::new(self.create_dependencies_tab()),
                        AnalysisTab::Suggestions => Box::new(self.create_suggestions_tab()),
                    };
                    view
                })
                .style(|s| s.flex_grow(1.0)),
            ))
        )
        .style(move |s| {
            s.size_full()
             .background(theme.colors.background)
        }))
    }

    /// Create the header
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let current_file = self.current_file;
        let is_analyzing = self.is_analyzing;
        let analysis_progress = self.analysis_progress;

        container(
            h_stack((
                label(|| "Code Analysis")
                    .style(|s| s.font_size(18.0).font_bold()),
                
                // Current file indicator
                container(
                    if let Some(file) = current_file.get() {
                        label(move || format!("Analyzing: {}", file))
                            .style(|s| s.margin_left(16.0).color(Color::rgb8(0xA0, 0xA0, 0xA0)))
                    } else {
                        label(|| "No file selected")
                            .style(|s| s.margin_left(16.0).color(Color::rgb8(0x80, 0x80, 0x80)))
                    }
                ),
                
                // Analysis status
                container(
                    if is_analyzing.get() {
                        h_stack((
                            label(|| "Analyzing...")
                                .style(|s| s.color(Color::rgb8(0x00, 0x7A, 0xCC))),
                            label(move || format!("{:.0}%", analysis_progress.get() * 100.0))
                                .style(|s| s.margin_left(8.0)),
                        ))
                    } else {
                        h_stack((
                            button("Analyze")
                                .action(|| {})
                                .style(|s| s.padding(4.0)),
                        ))
                    }
                ),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(16.0)
             .background(theme.colors.background_secondary)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create tab navigation
    fn create_tab_navigation(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let active_tab = self.active_tab;

        container(
            h_stack((
                self.create_tab_button("Overview", AnalysisTab::Overview),
                self.create_tab_button("Issues", AnalysisTab::Issues),
                self.create_tab_button("Metrics", AnalysisTab::Metrics),
                self.create_tab_button("Dependencies", AnalysisTab::Dependencies),
                self.create_tab_button("Suggestions", AnalysisTab::Suggestions),
            ))
        )
        .style(move |s| {
            s.width_full()
             .background(theme.colors.background_secondary)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create a tab button
    fn create_tab_button(&self, title: &str, tab: AnalysisTab) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let active_tab = self.active_tab;
        let title = title.to_string();

        button(label(move || title.clone()))
            .action(move || {
                active_tab.set(tab);
            })
            .style(move |s| {
                let base_style = s
                    .padding_horiz(16.0)
                    .padding_vert(8.0)
                    .border_bottom(2.0);

                if active_tab.get() == tab {
                    base_style
                        .background(theme.colors.background)
                        .border_color(theme.colors.accent)
                        .color(theme.colors.accent)
                } else {
                    base_style
                        .background(Color::TRANSPARENT)
                        .border_color(Color::TRANSPARENT)
                        .color(theme.colors.text)
                        .hover(|s| s.background(theme.colors.background_tertiary))
                }
            })
    }

    /// Create overview tab content
    fn create_overview_tab(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let metrics = self.metrics;
        let issues = self.issues;

        scroll(
            v_stack((
                // Summary cards
                h_stack((
                    self.create_metric_card("Lines of Code", move || {
                        metrics.get().map(|m| m.lines_of_code.to_string()).unwrap_or_else(|| "-".to_string())
                    }),
                    self.create_metric_card("Complexity", move || {
                        metrics.get().map(|m| format!("{:.1}", m.cyclomatic_complexity)).unwrap_or_else(|| "-".to_string())
                    }),
                    self.create_metric_card("Issues", move || {
                        issues.get().len().to_string()
                    }),
                ))
                .style(|s| s.gap(16.0).margin_bottom(16.0)),
                
                // Recent issues
                container(
                    v_stack((
                        label(|| "Recent Issues")
                            .style(|s| s.font_size(16.0).font_bold().margin_bottom(8.0)),
                        
                        container(
                            if issues.get().is_empty() {
                                v_stack((
                                    label(|| "No issues found")
                                        .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                                ))
                            } else {
                                v_stack_from_iter(
                                    issues.get().into_iter().take(5).map(|issue| {
                                        self.create_issue_item(issue)
                                    })
                                )
                            }
                        ),
                    ))
                )
                .style(move |s| {
                    s.width_full()
                     .padding(16.0)
                     .background(theme.colors.background_secondary)
                     .border_radius(8.0)
                }),
            ))
        )
        .style(|s| s.size_full().padding(16.0))
    }

    /// Create issues tab content
    fn create_issues_tab(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let issues = self.issues;

        scroll(
            v_stack((
                // Filters
                container(
                    h_stack((
                        label(|| "Filters:")
                            .style(|s| s.margin_right(16.0)),
                        button("All")
                            .action(|| {})
                            .style(|s| s.margin_right(8.0)),
                        button("Critical")
                            .action(|| {})
                            .style(|s| s.margin_right(8.0)),
                        button("High")
                            .action(|| {})
                            .style(|s| s.margin_right(8.0)),
                    ))
                )
                .style(move |s| {
                    s.width_full()
                     .padding(16.0)
                     .background(theme.colors.background_secondary)
                     .border_radius(8.0)
                     .margin_bottom(16.0)
                }),
                
                // Issues list
                container(
                    if issues.get().is_empty() {
                        v_stack((
                            label(|| "No issues found")
                                .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                        ))
                    } else {
                        v_stack_from_iter(
                            issues.get().into_iter().map(|issue| {
                                self.create_detailed_issue_item(issue)
                            })
                        )
                    }
                ),
            ))
        )
        .style(|s| s.size_full().padding(16.0))
    }

    /// Create metrics tab content
    fn create_metrics_tab(&self) -> impl View {
        let metrics = self.metrics;

        scroll(
            v_stack((
                label(|| "Code Metrics")
                    .style(|s| s.font_size(18.0).font_bold().margin_bottom(16.0)),
                
                container(
                    if let Some(m) = metrics.get() {
                        v_stack((
                            self.create_metric_row("Lines of Code", m.lines_of_code.to_string()),
                            self.create_metric_row("Cyclomatic Complexity", format!("{:.2}", m.cyclomatic_complexity)),
                            self.create_metric_row("Maintainability Index", format!("{:.2}", m.maintainability_index)),
                            self.create_metric_row("Technical Debt Ratio", format!("{:.2}%", m.technical_debt_ratio * 100.0)),
                            self.create_metric_row("Test Coverage", format!("{:.1}%", m.test_coverage * 100.0)),
                            self.create_metric_row("Code Duplication", format!("{:.1}%", m.duplication_percentage)),
                        ))
                    } else {
                        v_stack((
                            label(|| "No metrics available")
                                .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                        ))
                    }
                ),
            ))
        )
        .style(|s| s.size_full().padding(16.0))
    }

    /// Create dependencies tab content
    fn create_dependencies_tab(&self) -> impl View {
        scroll(
            v_stack((
                label(|| "Dependencies Analysis")
                    .style(|s| s.font_size(18.0).font_bold().margin_bottom(16.0)),
                
                label(|| "Dependency analysis will be implemented here")
                    .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
            ))
        )
        .style(|s| s.size_full().padding(16.0))
    }

    /// Create suggestions tab content
    fn create_suggestions_tab(&self) -> impl View {
        scroll(
            v_stack((
                label(|| "AI Suggestions")
                    .style(|s| s.font_size(18.0).font_bold().margin_bottom(16.0)),
                
                label(|| "AI-powered suggestions will be implemented here")
                    .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
            ))
        )
        .style(|s| s.size_full().padding(16.0))
    }

    /// Create a metric card
    fn create_metric_card(&self, title: &str, value_fn: impl Fn() -> String + 'static) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let title = title.to_string();

        container(
            v_stack((
                label(move || title.clone())
                    .style(|s| s.font_size(14.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                label(value_fn)
                    .style(|s| s.font_size(24.0).font_bold().margin_top(4.0)),
            ))
        )
        .style(move |s| {
            s.padding(16.0)
             .background(theme.colors.background_secondary)
             .border_radius(8.0)
             .min_width(120.0)
        })
    }

    /// Create a metric row
    fn create_metric_row(&self, label_text: &str, value: String) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let label_text = label_text.to_string();

        container(
            h_stack((
                label(move || label_text.clone())
                    .style(|s| s.flex_grow(1.0)),
                label(move || value.clone())
                    .style(|s| s.font_bold()),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create an issue item
    fn create_issue_item(&self, issue: CodeIssue) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();

        container(
            h_stack((
                label(move || issue.message.clone())
                    .style(|s| s.flex_grow(1.0)),
                label(move || format!("{:?}", issue.severity))
                    .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .margin_bottom(4.0)
             .background(theme.colors.background_secondary)
             .border_radius(4.0)
             .hover(|s| s.background(theme.colors.background_tertiary))
        })
    }

    /// Create a detailed issue item
    fn create_detailed_issue_item(&self, issue: CodeIssue) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();

        container(
            v_stack((
                h_stack((
                    label(move || issue.message.clone())
                        .style(|s| s.flex_grow(1.0).font_bold()),
                    label(move || format!("{:?}", issue.severity))
                        .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                )),
                label(move || format!("{}:{}", issue.file_path, issue.line_number))
                    .style(|s| s.font_size(12.0).color(Color::rgb8(0x80, 0x80, 0x80)).margin_top(4.0)),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(12.0)
             .margin_bottom(8.0)
             .background(theme.colors.background_secondary)
             .border_radius(8.0)
             .hover(|s| s.background(theme.colors.background_tertiary))
        })
    }

    /// Analyze current file
    pub fn analyze_file(&self, file_path: String) -> Result<(), UiError> {
        self.current_file.set(Some(file_path.clone()));
        self.is_analyzing.set(true);
        self.analysis_progress.set(0.0);

        // TODO: Implement actual analysis using core engine
        // For now, simulate analysis with dummy data
        self.simulate_analysis();

        Ok(())
    }

    /// Simulate analysis (placeholder)
    fn simulate_analysis(&self) {
        // Simulate progress
        self.analysis_progress.set(1.0);
        self.is_analyzing.set(false);

        // Set dummy metrics
        let dummy_metrics = CodeMetrics {
            lines_of_code: 1250,
            cyclomatic_complexity: 3.2,
            maintainability_index: 78.5,
            technical_debt_ratio: 0.15,
            test_coverage: 0.85,
            duplication_percentage: 5.2,
        };
        self.metrics.set(Some(dummy_metrics));

        // Set dummy issues
        let dummy_issues = vec![
            CodeIssue {
                id: "1".to_string(),
                severity: IssueSeverity::High,
                category: IssueCategory::Security,
                message: "Potential SQL injection vulnerability".to_string(),
                file_path: "src/database.rs".to_string(),
                line_number: 45,
                column: 12,
                suggestion: Some("Use parameterized queries".to_string()),
            },
            CodeIssue {
                id: "2".to_string(),
                severity: IssueSeverity::Medium,
                category: IssueCategory::Performance,
                message: "Inefficient loop detected".to_string(),
                file_path: "src/utils.rs".to_string(),
                line_number: 123,
                column: 8,
                suggestion: Some("Consider using iterator methods".to_string()),
            },
        ];
        self.issues.set(dummy_issues);
    }
}

impl Clone for CodeAnalysisPanel {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            active_tab: self.active_tab,
            current_file: self.current_file,
            metrics: self.metrics,
            issues: self.issues,
            is_analyzing: self.is_analyzing,
            analysis_progress: self.analysis_progress,
            selected_issue: self.selected_issue,
            severity_filter: self.severity_filter,
            category_filter: self.category_filter,
        }
    }
}
