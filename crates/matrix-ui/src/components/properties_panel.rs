//! Properties Panel Component
//!
//! This module provides a dynamic properties panel that can display and edit
//! properties of selected items (files, nodes, components, etc.).

use std::sync::Arc;
use std::collections::HashMap;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{container, h_stack, v_stack, v_stack_from_iter, label, button, scroll, Decorators},
    View,
    peniko::Color,
};
use serde::{Serialize, Deserialize};

use crate::theme::ThemeManager;
use crate::error::UiError;
use matrix_core::Engine as CoreEngine;

/// Property value types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PropertyValue {
    String(String),
    Integer(i64),
    Float(f64),
    Boolean(bool),
    Color(String),
    Path(String),
    Array(Vec<PropertyValue>),
    Object(HashMap<String, PropertyValue>),
}

/// Property definition with metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Property {
    pub key: String,
    pub display_name: String,
    pub value: PropertyValue,
    pub property_type: PropertyType,
    pub is_editable: bool,
    pub is_required: bool,
    pub description: Option<String>,
    pub category: String,
}

/// Property types for UI rendering
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PropertyType {
    Text,
    Number,
    Boolean,
    Color,
    File,
    Directory,
    Enum(Vec<String>),
    Range { min: f64, max: f64 },
    MultiLine,
    ReadOnly,
}

/// Property categories for organization
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum PropertyCategory {
    General,
    Appearance,
    Behavior,
    Performance,
    Advanced,
    Custom(String),
}

/// Properties panel component
pub struct PropertiesPanel {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    
    /// Current properties
    properties: RwSignal<Vec<Property>>,
    
    /// Selected item info
    selected_item: RwSignal<Option<SelectedItem>>,
    
    /// Expanded categories
    expanded_categories: RwSignal<HashMap<String, bool>>,
    
    /// Search filter
    search_filter: RwSignal<String>,
    
    /// Show advanced properties
    show_advanced: RwSignal<bool>,
    
    /// Modified properties (unsaved changes)
    modified_properties: RwSignal<HashMap<String, PropertyValue>>,
    
    /// Validation errors
    validation_errors: RwSignal<HashMap<String, String>>,
}

/// Information about the selected item
#[derive(Debug, Clone)]
pub struct SelectedItem {
    pub id: String,
    pub name: String,
    pub item_type: String,
    pub icon: String,
}

impl PropertiesPanel {
    /// Create a new properties panel
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Arc<Self>, UiError> {
        Ok(Arc::new(Self {
            core,
            theme_manager,
            properties: create_rw_signal(Vec::new()),
            selected_item: create_rw_signal(None),
            expanded_categories: create_rw_signal(HashMap::new()),
            search_filter: create_rw_signal(String::new()),
            show_advanced: create_rw_signal(false),
            modified_properties: create_rw_signal(HashMap::new()),
            validation_errors: create_rw_signal(HashMap::new()),
        }))
    }

    /// Create the main view
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let selected_item = self.selected_item;
        let properties = self.properties;

        container(
            v_stack((
                // Header
                self.create_header(),
                
                // Content
                container(
                    if selected_item.get().is_none() {
                        v_stack((
                            label(|| "No item selected")
                                .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                        ))
                    } else if properties.get().is_empty() {
                        v_stack((
                            label(|| "No properties available")
                                .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                        ))
                    } else {
                        v_stack((
                            scroll(
                                self.create_properties_view()
                            ),
                        ))
                    }
                )
                .style(|s| s.flex_grow(1.0)),
                
                // Footer with actions
                self.create_footer(),
            ))
        )
        .style(move |s| {
            s.size_full()
             .background(theme.colors.background)
        })
    }

    /// Create the header
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let selected_item = self.selected_item;
        let search_filter = self.search_filter;
        let show_advanced = self.show_advanced;

        container(
            v_stack((
                // Selected item info
                container(
                    if let Some(item) = selected_item.get() {
                        h_stack((
                            label(move || item.icon.clone())
                                .style(|s| s.margin_right(8.0)),
                            v_stack((
                                label(move || item.name.clone())
                                    .style(|s| s.font_bold()),
                                label(move || item.item_type.clone())
                                    .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                            )),
                        ))
                    } else {
                        h_stack((
                            label(|| "Properties")
                                .style(|s| s.font_bold().font_size(16.0)),
                        ))
                    }
                )
                .style(|s| s.margin_bottom(8.0)),
                
                // Controls
                h_stack((
                    // Search
                    floem::views::text_input(search_filter)
                        .placeholder("Search properties...".to_string())
                        .style(move |s| {
                            s.flex_grow(1.0)
                             .padding(4.0)
                             .border(1.0)
                             .border_color(theme.colors.border)
                             .border_radius(4.0)
                             .margin_right(8.0)
                        }),
                    
                    // Advanced toggle
                    button("Toggle")
                        .style(|s| s.padding(4.0))
                    .action({
                        let show_advanced = self.show_advanced;
                        move || {
                            show_advanced.set(!show_advanced.get());
                        }
                    })
                    .style(|s| s.padding(4.0)),
                )),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(12.0)
             .background(theme.colors.background_secondary)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create properties view
    fn create_properties_view(&self) -> impl View {
        let properties = self.properties;
        let search_filter = self.search_filter;
        let show_advanced = self.show_advanced;

        container(
            v_stack_from_iter(
                properties.get().into_iter()
                    .filter(|prop| {
                        let search = search_filter.get().to_lowercase();
                        let matches_search = search.is_empty() || 
                            prop.display_name.to_lowercase().contains(&search) ||
                            prop.key.to_lowercase().contains(&search);
                        
                        let matches_advanced = show_advanced.get() || prop.category != "Advanced";
                        
                        matches_search && matches_advanced
                    })
                    .map(|prop| self.create_property_item(prop))
            )
        )
        .style(|s| s.width_full().padding(8.0))
    }

    /// Create a property item
    fn create_property_item(&self, property: Property) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let modified_properties = self.modified_properties;
        let validation_errors = self.validation_errors;
        let is_modified = modified_properties.get().contains_key(&property.key);
        let has_error = validation_errors.get().contains_key(&property.key);

        container(
            v_stack((
                // Property header
                h_stack((
                    // Name and description
                    v_stack((
                        label(move || property.display_name.clone())
                            .style(move |s| {
                                let base_style = s.font_bold();
                                if is_modified {
                                    base_style.color(theme.colors.accent)
                                } else if has_error {
                                    base_style.color(Color::rgb8(0xFF, 0x6B, 0x6B))
                                } else {
                                    base_style.color(theme.colors.text)
                                }
                            }),
                        
                        if let Some(desc) = property.description.clone() {
                            label(move || desc)
                                .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0)))
                        } else {
                            label(|| "")
                                .style(|s| s.height(0.0))
                        },
                    ))
                    .style(|s| s.flex_grow(1.0)),
                    
                    // Reset button (if modified)
                    container(
                        if is_modified {
                            button("↺")
                                .action({
                                    let key = property.key.clone();
                                    let modified_properties = self.modified_properties;
                                    move || {
                                        let mut modified = modified_properties.get();
                                        modified.remove(&key);
                                        modified_properties.set(modified);
                                    }
                                })
                                .style(|s| s.font_size(12.0).padding(2.0))
                        } else {
                            button("")
                                .style(|s| s.font_size(12.0).padding(2.0).width(0.0))
                        }
                    ),
                )),
                
                // Property value editor
                container(
                    self.create_property_editor(&property)
                )
                .style(|s| s.margin_top(4.0)),
                
                // Error message
                if has_error {
                    label({
                        let key = property.key.clone();
                        move || {
                            validation_errors.get().get(&key).cloned().unwrap_or_default()
                        }
                    })
                    .style(|s| s.font_size(12.0).color(Color::rgb8(0xFF, 0x6B, 0x6B)).margin_top(2.0))
                } else {
                    label(|| "")
                        .style(|s| s.height(0.0))
                },
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .margin_bottom(4.0)
             .background(theme.colors.background_secondary)
             .border_radius(4.0)
             .border_left(3.0)
             .border_color(if is_modified {
                 theme.colors.accent
             } else if has_error {
                 Color::rgb8(0xFF, 0x6B, 0x6B)
             } else {
                 Color::TRANSPARENT
             })
        })
    }

    /// Create property editor based on type
    fn create_property_editor(&self, property: &Property) -> Box<dyn View> {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();

        match &property.property_type {
            PropertyType::Text => {
                let value = match &property.value {
                    PropertyValue::String(s) => s.clone(),
                    _ => String::new(),
                };
                
                Box::new(floem::views::text_input(create_rw_signal(value))
                    .style(move |s| {
                        s.width_full()
                         .padding(4.0)
                         .border(1.0)
                         .border_color(theme.colors.border)
                         .border_radius(4.0)
                    }))
            }
            
            PropertyType::Boolean => {
                let checked = match &property.value {
                    PropertyValue::Boolean(b) => *b,
                    _ => false,
                };
                
                Box::new(h_stack((
                    button(if checked { "☑" } else { "☐" })
                        .action(|| {})
                        .style(|s| s.margin_right(8.0)),
                    label(|| if checked { "True" } else { "False" })
                        .style(|s| s.color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                )))
            }
            
            PropertyType::ReadOnly => {
                let display_value = self.format_property_value(&property.value);
                label(move || display_value.clone())
                    .style(|s| s.color(Color::rgb8(0xA0, 0xA0, 0xA0)))
            }
            
            _ => {
                // Default fallback
                let display_value = self.format_property_value(&property.value);
                label(move || display_value.clone())
                    .style(|s| s.color(Color::rgb8(0xA0, 0xA0, 0xA0)))
            }
        }
    }

    /// Create footer with action buttons
    fn create_footer(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let modified_properties = self.modified_properties;
        let has_changes = !modified_properties.get().is_empty();

        container(
            h_stack((
                // Status
                label({
                    move || {
                        let count = modified_properties.get().len();
                        if count > 0 {
                            format!("{} unsaved changes", count)
                        } else {
                            "No changes".to_string()
                        }
                    }
                })
                .style(|s| s.flex_grow(1.0).font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                
                // Actions
                if has_changes {
                    h_stack((
                        button("Revert")
                            .action({
                                let modified_properties = self.modified_properties;
                                move || {
                                    modified_properties.set(HashMap::new());
                                }
                            })
                            .style(|s| s.margin_right(8.0).padding(4.0)),
                        
                        button("Apply")
                            .action({
                                let properties_panel = self.clone();
                                move || {
                                    properties_panel.apply_changes();
                                }
                            })
                            .style(|s| s.padding(4.0)),
                    ))
                } else {
                    container(label(|| ""))
                },
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(12.0)
             .background(theme.colors.background_secondary)
             .border_top(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Format property value for display
    fn format_property_value(&self, value: &PropertyValue) -> String {
        match value {
            PropertyValue::String(s) => s.clone(),
            PropertyValue::Integer(i) => i.to_string(),
            PropertyValue::Float(f) => format!("{:.2}", f),
            PropertyValue::Boolean(b) => b.to_string(),
            PropertyValue::Color(c) => c.clone(),
            PropertyValue::Path(p) => p.clone(),
            PropertyValue::Array(arr) => format!("[{} items]", arr.len()),
            PropertyValue::Object(obj) => format!("{{{} properties}}", obj.len()),
        }
    }

    /// Set properties for selected item
    pub fn set_properties(&self, item: SelectedItem, properties: Vec<Property>) {
        self.selected_item.set(Some(item));
        self.properties.set(properties);
        self.modified_properties.set(HashMap::new());
        self.validation_errors.set(HashMap::new());
    }

    /// Clear properties
    pub fn clear_properties(&self) {
        self.selected_item.set(None);
        self.properties.set(Vec::new());
        self.modified_properties.set(HashMap::new());
        self.validation_errors.set(HashMap::new());
    }

    /// Apply changes
    fn apply_changes(&self) {
        // TODO: Implement actual property application
        // For now, just clear modified properties
        self.modified_properties.set(HashMap::new());
        self.validation_errors.set(HashMap::new());
    }

    /// Get current properties
    pub fn get_properties(&self) -> Vec<Property> {
        self.properties.get()
    }

    /// Get modified properties
    pub fn get_modified_properties(&self) -> HashMap<String, PropertyValue> {
        self.modified_properties.get()
    }
}

impl Clone for PropertiesPanel {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            properties: self.properties,
            selected_item: self.selected_item,
            expanded_categories: self.expanded_categories,
            search_filter: self.search_filter,
            show_advanced: self.show_advanced,
            modified_properties: self.modified_properties,
            validation_errors: self.validation_errors,
        }
    }
}
