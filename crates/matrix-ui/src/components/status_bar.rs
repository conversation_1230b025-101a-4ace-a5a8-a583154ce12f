//! Componente barra di stato
//!
//! Questo modulo fornisce una barra di stato personalizzata per l'IDE MATRIX.

use std::sync::Arc;
use floem::reactive::{RwSignal, SignalGet};
use floem::{View, views::{h_stack, h_stack_from_iter, label, empty, container, Decorators}};

use crate::theme::ThemeManager;

/// Tipo di indicatore di stato
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[allow(dead_code)]
pub enum StatusType {
    /// Stato normale
    Normal,

    /// Stato di successo
    Success,

    /// Stato di errore
    Error,

    /// Stato di avviso
    Warning,

    /// Stato informativo
    Info,
}

/// Elemento della barra di stato
#[allow(dead_code)]
pub struct StatusItem {
    /// Testo dell'elemento
    pub text: RwSignal<String>,

    /// Tipo di stato
    pub status_type: RwSignal<StatusType>,

    /// Tooltip dell'elemento
    pub tooltip: Option<String>,

    /// È cliccabile?
    pub clickable: bool,

    /// Callback quando viene cliccato
    pub on_click: Option<Box<dyn Fn() + Send + Sync + 'static>>,
}

impl StatusItem {
    /// Crea un nuovo elemento della barra di stato
    #[allow(dead_code)]
    pub fn new(text: RwSignal<String>, status_type: RwSignal<StatusType>) -> Self {
        Self {
            text,
            status_type,
            tooltip: None,
            clickable: false,
            on_click: None,
        }
    }

    /// Aggiunge un tooltip
    #[allow(dead_code)]
    pub fn with_tooltip(mut self, tooltip: &str) -> Self {
        self.tooltip = Some(tooltip.to_string());
        self
    }

    /// Rende l'elemento cliccabile
    #[allow(dead_code)]
    pub fn with_click<F>(mut self, callback: F) -> Self
    where
        F: Fn() + Send + Sync + 'static,
    {
        self.clickable = true;
        self.on_click = Some(Box::new(callback));
        self
    }
}

/// Crea la barra di stato
#[allow(dead_code)]
pub fn status_bar(
    theme_manager: Arc<ThemeManager>,
    left_items: Vec<StatusItem>,
    right_items: Vec<StatusItem>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    h_stack((
        // Elementi a sinistra
        h_stack_from_iter(left_items.into_iter().map(|item| {
            create_status_item(theme_manager.clone(), item)
        }))
        .style(|s| s.gap(16.0)),

        // Spazio flessibile
        empty().style(|s| s.flex_grow(1.0)),

        // Elementi a destra
        h_stack_from_iter(right_items.into_iter().map(|item| {
            create_status_item(theme_manager.clone(), item)
        }))
        .style(|s| s.gap(16.0)),
    ))
    .style(move |s| {
        s.width_full()
         .height(28.0)
         .padding_horiz(8.0)
         .background(theme.colors.background_secondary)
         .border_top(theme.borders.width_thin)
         .border_color(theme.colors.border)
         .color(theme.colors.text_secondary)
         .font_size(theme.font_sizes.xs)
    })
}

/// Crea un elemento della barra di stato
#[allow(dead_code)]
fn create_status_item(
    theme_manager: Arc<ThemeManager>,
    item: StatusItem,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let text = item.text;
    let status_type = item.status_type;
    let clickable = item.clickable;

    let view = label(move || text.get())
        .style(move |s| {
            let base_style = s.padding_horiz(4.0)
                             .padding_vert(2.0)
                             .font_size(theme.font_sizes.xs);

            // Applica stili in base al tipo di stato
            match status_type.get() {
                StatusType::Normal => base_style.color(theme.colors.text_secondary),
                StatusType::Success => base_style.color(theme.colors.success),
                StatusType::Error => base_style.color(theme.colors.error),
                StatusType::Warning => base_style.color(theme.colors.warning),
                StatusType::Info => base_style.color(theme.colors.info),
            }
        });

    // Se è cliccabile, aggiungiamo gli stili e il callback
    if clickable {
        if let Some(on_click) = item.on_click {
            container(view)
                .on_click_stop(move |_| {
                    on_click();
                })
                .style(move |s| {
                    s.cursor(floem::style::CursorStyle::Pointer)
                     .hover(|s| s.color(theme.colors.accent))
                })
        } else {
            container(view)
        }
    } else {
        container(view)
    }
}

/// Crea una barra di stato semplice con un messaggio
#[allow(dead_code)]
pub fn simple_status_bar(
    theme_manager: Arc<ThemeManager>,
    message: RwSignal<String>,
    status: RwSignal<StatusType>,
) -> impl View {
    let item = StatusItem::new(message, status);
    status_bar(theme_manager, vec![item], vec![])
}
