//! Performance Monitoring Widget
//!
//! This module provides a real-time performance monitoring widget that displays
//! system metrics, application performance, and resource usage.

use std::sync::Arc;
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{container, dyn_container, h_stack, v_stack, v_stack_from_iter, label, button, Decorators},
    style::{AlignItems, JustifyContent},
    View, IntoView,
    peniko::Color,
};
use serde::{Serialize, Deserialize};

use crate::theme::ThemeManager;
use crate::error::UiError;

/// Performance metrics data point
#[derive(Debug, Clone)]
pub struct PerformanceMetric {
    pub timestamp: Instant,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub memory_total: u64,
    pub fps: f32,
    pub render_time: f32,
    pub event_processing_time: f32,
}

/// Performance alert levels
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum AlertLevel {
    Normal,
    Warning,
    Critical,
}

/// Performance alert
#[derive(Debug, Clone)]
pub struct PerformanceAlert {
    pub level: AlertLevel,
    pub message: String,
    pub timestamp: Instant,
    pub metric_type: String,
    pub value: f32,
    pub threshold: f32,
}

/// Performance monitoring configuration
pub struct MonitorConfig {
    pub update_interval: Duration,
    pub history_size: usize,
    pub cpu_warning_threshold: f32,
    pub cpu_critical_threshold: f32,
    pub memory_warning_threshold: f32,
    pub memory_critical_threshold: f32,
    pub fps_warning_threshold: f32,
    pub render_time_warning_threshold: f32,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            update_interval: Duration::from_millis(1000),
            history_size: 60, // 1 minute of data at 1Hz
            cpu_warning_threshold: 70.0,
            cpu_critical_threshold: 90.0,
            memory_warning_threshold: 80.0,
            memory_critical_threshold: 95.0,
            fps_warning_threshold: 30.0,
            render_time_warning_threshold: 16.67, // 60 FPS threshold
        }
    }
}

/// Performance monitoring widget
pub struct PerformanceMonitor {
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    
    /// Configuration
    config: MonitorConfig,
    
    /// Current metrics
    current_metrics: RwSignal<Option<PerformanceMetric>>,
    
    /// Metrics history
    metrics_history: RwSignal<VecDeque<PerformanceMetric>>,
    
    /// Active alerts
    alerts: RwSignal<Vec<PerformanceAlert>>,
    
    /// Monitoring enabled
    monitoring_enabled: RwSignal<bool>,
    
    /// Display mode
    display_mode: RwSignal<DisplayMode>,
    
    /// Last update time
    last_update: RwSignal<Option<Instant>>,
    
    /// Update counter
    update_counter: RwSignal<u64>,
}

/// Display modes for the performance monitor
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum DisplayMode {
    Compact,
    Detailed,
    Graph,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new(theme_manager: Arc<ThemeManager>) -> Result<Arc<Self>, UiError> {
        Ok(Arc::new(Self {
            theme_manager,
            config: MonitorConfig::default(),
            current_metrics: create_rw_signal(None),
            metrics_history: create_rw_signal(VecDeque::new()),
            alerts: create_rw_signal(Vec::new()),
            monitoring_enabled: create_rw_signal(true),
            display_mode: create_rw_signal(DisplayMode::Compact),
            last_update: create_rw_signal(None),
            update_counter: create_rw_signal(0),
        }))
    }

    /// Create the main view
    pub fn create_view(&self) -> Box<dyn View> {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let display_mode = self.display_mode;

        // Use a simple match to return the appropriate view
        let view = match display_mode.get() {
            DisplayMode::Compact => Box::new(self.create_compact_view()) as Box<dyn View>,
            DisplayMode::Detailed => Box::new(self.create_detailed_view()) as Box<dyn View>,
            DisplayMode::Graph => Box::new(self.create_graph_view()) as Box<dyn View>,
        };

        Box::new(
            container(view)
                .style(move |s| {
                    s.background(theme.colors.background_secondary)
                     .border_radius(8.0)
                     .padding(8.0)
                })
        )
    }

    /// Create compact view
    fn create_compact_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let current_metrics = self.current_metrics;
        let monitoring_enabled = self.monitoring_enabled;
        let alerts = self.alerts;

        h_stack((
            // Status indicator
            container(label(|| "●"))
                .style({
                    move |s| {
                        let alert_count = alerts.get().len();
                        let color = if alert_count > 0 {
                            Color::rgb8(0xFF, 0x6B, 0x6B) // Red for alerts
                        } else if monitoring_enabled.get() {
                            Color::rgb8(0x4A, 0xDE, 0x80) // Green for normal
                        } else {
                            Color::rgb8(0x9C, 0xA3, 0xAF) // Gray for disabled
                        };
                        s.color(color).font_size(12.0)
                    }
                }),
            
            // CPU usage
            label({
                move || {
                    if let Some(metrics) = current_metrics.get() {
                        format!("CPU: {:.1}%", metrics.cpu_usage)
                    } else {
                        "CPU: --".to_string()
                    }
                }
            })
            .style(|s| s.font_size(12.0).margin_left(8.0)),
            
            // Memory usage
            label({
                move || {
                    if let Some(metrics) = current_metrics.get() {
                        format!("MEM: {:.1}%", metrics.memory_usage)
                    } else {
                        "MEM: --".to_string()
                    }
                }
            })
            .style(|s| s.font_size(12.0).margin_left(8.0)),
            
            // FPS
            label({
                move || {
                    if let Some(metrics) = current_metrics.get() {
                        format!("FPS: {:.0}", metrics.fps)
                    } else {
                        "FPS: --".to_string()
                    }
                }
            })
            .style(|s| s.font_size(12.0).margin_left(8.0)),
            
            // Toggle button
            button("⚙")
                .action({
                    let display_mode = self.display_mode;
                    move || {
                        display_mode.set(DisplayMode::Detailed);
                    }
                })
                .style(|s| s.font_size(12.0).margin_left(8.0).padding(2.0)),
        ))
        .style(|s| s.align_items(AlignItems::Center))
    }

    /// Create detailed view
    fn create_detailed_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let current_metrics = self.current_metrics;
        let alerts = self.alerts;
        let monitoring_enabled = self.monitoring_enabled;

        v_stack((
            // Header
            h_stack((
                label(|| "Performance Monitor")
                    .style(|s| s.font_bold().font_size(14.0)),
                
                button("Compact")
                    .action({
                        let display_mode = self.display_mode;
                        move || {
                            display_mode.set(DisplayMode::Compact);
                        }
                    })
                    .style(|s| s.font_size(12.0).margin_left(8.0).padding(2.0)),
                
                button("Toggle")
                    .action({
                        let monitoring_enabled = self.monitoring_enabled;
                        move || {
                            monitoring_enabled.set(!monitoring_enabled.get());
                        }
                    })
                .action({
                    let monitoring_enabled = self.monitoring_enabled;
                    move || {
                        monitoring_enabled.set(!monitoring_enabled.get());
                    }
                })
                .style(|s| s.font_size(12.0).margin_left(4.0).padding(2.0)),
            ))
            .style(|s| s.justify_content(JustifyContent::SpaceBetween).margin_bottom(8.0)),
            
            // Metrics grid - simplified for now
            container(
                if let Some(metrics) = current_metrics.get() {
                    v_stack((
                        h_stack((
                            self.create_metric_item("CPU Usage", format!("{:.1}%", metrics.cpu_usage),
                                self.get_alert_color(metrics.cpu_usage, 70.0, 90.0)),
                            self.create_metric_item("Memory Usage", format!("{:.1}%", metrics.memory_usage),
                                self.get_alert_color(metrics.memory_usage, 80.0, 95.0)),
                        ))
                        .style(|s| s.gap(8.0)),

                        h_stack((
                            self.create_metric_item("FPS", format!("{:.0}", metrics.fps),
                                if metrics.fps < 30.0 { Color::rgb8(0xFF, 0x6B, 0x6B) } else { Color::rgb8(0x4A, 0xDE, 0x80) }),
                            self.create_metric_item("Render Time", format!("{:.2}ms", metrics.render_time),
                                if metrics.render_time > 16.67 { Color::rgb8(0xFF, 0x6B, 0x6B) } else { Color::rgb8(0x4A, 0xDE, 0x80) }),
                        ))
                        .style(|s| s.gap(8.0).margin_top(8.0)),
                    ))
                } else {
                    v_stack((
                        label(|| "No metrics available")
                            .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                    ))
                }
            ),
            
            // Alerts section - simplified
            container(
                if alerts.get().is_empty() {
                    v_stack((
                        label(|| "No alerts")
                            .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80)).font_size(12.0)),
                    ))
                } else {
                    v_stack((
                        label(|| "Alerts")
                            .style(|s| s.font_bold().font_size(12.0).margin_bottom(4.0)),
                        v_stack_from_iter(
                            alerts.get().into_iter().take(3).map(|alert| {
                                self.create_alert_item(alert)
                            })
                        )
                    ))
                }
            )
            .style(move |s| {
                s.width_full()
                 .margin_top(8.0)
                 .padding(8.0)
                 .background(theme.colors.background)
                 .border_radius(4.0)
            }),
        ))
        .style(|s| s.width(300.0))
    }

    /// Create graph view
    fn create_graph_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let metrics_history = self.metrics_history;

        v_stack((
            // Header
            h_stack((
                label(|| "Performance Graph")
                    .style(|s| s.font_bold().font_size(14.0)),
                
                button("Detailed")
                    .action({
                        let display_mode = self.display_mode;
                        move || {
                            display_mode.set(DisplayMode::Detailed);
                        }
                    })
                    .style(|s| s.font_size(12.0).margin_left(8.0).padding(2.0)),
            ))
            .style(|s| s.justify_content(JustifyContent::SpaceBetween).margin_bottom(8.0)),
            
            // Graph area (placeholder)
            container(
                label(|| "Performance graph visualization will be implemented here")
                    .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80)))
            )
            .style(move |s| {
                s.size(400.0, 200.0)
                 .background(theme.colors.background)
                 .border_radius(4.0)
                 .justify_center()
                 .items_center()
            }),
            
            // Legend
            h_stack((
                self.create_legend_item("CPU", Color::rgb8(0xFF, 0x6B, 0x6B)),
                self.create_legend_item("Memory", Color::rgb8(0x4A, 0xDE, 0x80)),
                self.create_legend_item("FPS", Color::rgb8(0x60, 0xA5, 0xFA)),
            ))
            .style(|s| s.gap(16.0).margin_top(8.0).justify_center()),
        ))
        .style(|s| s.width(450.0))
    }

    /// Create a metric item
    fn create_metric_item(&self, label_text: &str, value: String, color: Color) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let label_text = label_text.to_string();

        container(
            v_stack((
                label(move || label_text.clone())
                    .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0))),
                label(move || value.clone())
                    .style(move |s| s.font_bold().font_size(16.0).color(color)),
            ))
        )
        .style(move |s| {
            s.padding(8.0)
             .background(theme.colors.background)
             .border_radius(4.0)
             .min_width(80.0)
             .align_items(AlignItems::Center)
        })
    }

    /// Create an alert item
    fn create_alert_item(&self, alert: PerformanceAlert) -> impl View {
        let color = match alert.level {
            AlertLevel::Critical => Color::rgb8(0xFF, 0x6B, 0x6B),
            AlertLevel::Warning => Color::rgb8(0xFF, 0xD9, 0x3D),
            AlertLevel::Normal => Color::rgb8(0x4A, 0xDE, 0x80),
        };

        h_stack((
            label(|| "●")
                .style(move |s| s.color(color).font_size(12.0).margin_right(4.0)),
            label(move || alert.message.clone())
                .style(|s| s.font_size(12.0).flex_grow(1.0)),
        ))
        .style(|s| s.margin_bottom(2.0))
    }

    /// Create a legend item
    fn create_legend_item(&self, label_text: &str, color: Color) -> impl View {
        let label_text = label_text.to_string();

        h_stack((
            container(label(|| "■"))
                .style(move |s| s.color(color).font_size(12.0).margin_right(4.0)),
            label(move || label_text.clone())
                .style(|s| s.font_size(12.0)),
        ))
    }

    /// Get alert color based on thresholds
    fn get_alert_color(&self, value: f32, warning_threshold: f32, critical_threshold: f32) -> Color {
        if value >= critical_threshold {
            Color::rgb8(0xFF, 0x6B, 0x6B) // Red
        } else if value >= warning_threshold {
            Color::rgb8(0xFF, 0xD9, 0x3D) // Yellow
        } else {
            Color::rgb8(0x4A, 0xDE, 0x80) // Green
        }
    }

    /// Update metrics
    pub fn update_metrics(&self, metrics: PerformanceMetric) {
        self.current_metrics.set(Some(metrics.clone()));
        self.last_update.set(Some(Instant::now()));
        self.update_counter.set(self.update_counter.get() + 1);

        // Add to history
        let mut history = self.metrics_history.get();
        history.push_back(metrics.clone());
        
        // Keep history size limited
        while history.len() > self.config.history_size {
            history.pop_front();
        }
        self.metrics_history.set(history);

        // Check for alerts
        self.check_alerts(&metrics);
    }

    /// Check for performance alerts
    fn check_alerts(&self, metrics: &PerformanceMetric) {
        let mut new_alerts = Vec::new();
        let now = Instant::now();

        // CPU alerts
        if metrics.cpu_usage >= self.config.cpu_critical_threshold {
            new_alerts.push(PerformanceAlert {
                level: AlertLevel::Critical,
                message: format!("Critical CPU usage: {:.1}%", metrics.cpu_usage),
                timestamp: now,
                metric_type: "cpu".to_string(),
                value: metrics.cpu_usage,
                threshold: self.config.cpu_critical_threshold,
            });
        } else if metrics.cpu_usage >= self.config.cpu_warning_threshold {
            new_alerts.push(PerformanceAlert {
                level: AlertLevel::Warning,
                message: format!("High CPU usage: {:.1}%", metrics.cpu_usage),
                timestamp: now,
                metric_type: "cpu".to_string(),
                value: metrics.cpu_usage,
                threshold: self.config.cpu_warning_threshold,
            });
        }

        // Memory alerts
        if metrics.memory_usage >= self.config.memory_critical_threshold {
            new_alerts.push(PerformanceAlert {
                level: AlertLevel::Critical,
                message: format!("Critical memory usage: {:.1}%", metrics.memory_usage),
                timestamp: now,
                metric_type: "memory".to_string(),
                value: metrics.memory_usage,
                threshold: self.config.memory_critical_threshold,
            });
        } else if metrics.memory_usage >= self.config.memory_warning_threshold {
            new_alerts.push(PerformanceAlert {
                level: AlertLevel::Warning,
                message: format!("High memory usage: {:.1}%", metrics.memory_usage),
                timestamp: now,
                metric_type: "memory".to_string(),
                value: metrics.memory_usage,
                threshold: self.config.memory_warning_threshold,
            });
        }

        // FPS alerts
        if metrics.fps < self.config.fps_warning_threshold {
            new_alerts.push(PerformanceAlert {
                level: AlertLevel::Warning,
                message: format!("Low FPS: {:.0}", metrics.fps),
                timestamp: now,
                metric_type: "fps".to_string(),
                value: metrics.fps,
                threshold: self.config.fps_warning_threshold,
            });
        }

        // Update alerts (keep only recent ones)
        let mut current_alerts = self.alerts.get();
        current_alerts.extend(new_alerts);
        
        // Remove old alerts (older than 5 minutes)
        let cutoff = now - Duration::from_secs(300);
        current_alerts.retain(|alert| alert.timestamp > cutoff);
        
        // Keep only the most recent 10 alerts
        if current_alerts.len() > 10 {
            current_alerts.drain(0..current_alerts.len() - 10);
        }
        
        self.alerts.set(current_alerts);
    }

    /// Enable/disable monitoring
    pub fn set_monitoring_enabled(&self, enabled: bool) {
        self.monitoring_enabled.set(enabled);
    }

    /// Set display mode
    pub fn set_display_mode(&self, mode: DisplayMode) {
        self.display_mode.set(mode);
    }

    /// Get current metrics
    pub fn get_current_metrics(&self) -> Option<PerformanceMetric> {
        self.current_metrics.get()
    }

    /// Get metrics history
    pub fn get_metrics_history(&self) -> VecDeque<PerformanceMetric> {
        self.metrics_history.get()
    }

    /// Clear alerts
    pub fn clear_alerts(&self) {
        self.alerts.set(Vec::new());
    }
}

impl Clone for PerformanceMonitor {
    fn clone(&self) -> Self {
        Self {
            theme_manager: self.theme_manager.clone(),
            config: MonitorConfig::default(), // Reset config on clone
            current_metrics: self.current_metrics,
            metrics_history: self.metrics_history,
            alerts: self.alerts,
            monitoring_enabled: self.monitoring_enabled,
            display_mode: self.display_mode,
            last_update: self.last_update,
            update_counter: self.update_counter,
        }
    }
}
