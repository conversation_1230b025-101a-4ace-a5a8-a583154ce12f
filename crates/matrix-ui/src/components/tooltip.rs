//! Componente tooltip
//!
//! Questo modulo fornisce un componente tooltip per l'IDE MATRIX.

use std::sync::Arc;
use floem::{View, views::{container, label, Decorators}};

use floem::unit::PxPct;
use floem::peniko::Color;
use crate::theme::ThemeManager;

/// Posizione del tooltip
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[allow(dead_code)]
pub enum TooltipPosition {
    /// Sopra l'elemento
    Top,

    /// Sotto l'elemento
    Bottom,

    /// A sinistra dell'elemento
    Left,

    /// A destra dell'elemento
    Right,
}

/// Configurazione del tooltip
#[allow(dead_code)]
pub struct TooltipConfig {
    /// Posizione del tooltip
    pub position: TooltipPosition,

    /// Ritardo prima della visualizzazione (in millisecondi)
    pub delay_ms: u64,

    /// Durata della visualizzazione (in millisecondi, 0 per infinito)
    pub duration_ms: u64,
}

impl Default for TooltipConfig {
    fn default() -> Self {
        Self {
            position: TooltipPosition::Bottom,
            delay_ms: 500,
            duration_ms: 0, // infinito
        }
    }
}

/// Aggiunge un tooltip a una vista
#[allow(dead_code)]
pub fn with_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
    config: Option<TooltipConfig>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let config = config.unwrap_or_default();
    let tooltip_text = text.to_string();

    // Crea il tooltip
    let _ = label(move || tooltip_text.clone())
        .style(move |s| {
            s.position(floem::style::Position::Absolute)
             .z_index(1000)
             .padding(8.0)
             .border_radius(theme.borders.radius_small)
             .background(theme.colors.background_tertiary)
             .color(theme.colors.text)
             .font_size(theme.font_sizes.xs)
             .box_shadow_color(Color::rgba8(0, 0, 0, 100))
             .box_shadow_h_offset(PxPct::Px(0.0))
             .box_shadow_v_offset(PxPct::Px(2.0))
             .box_shadow_blur(PxPct::Px(4.0))
             .box_shadow_spread(PxPct::Px(0.0))
             // Posiziona il tooltip in base alla configurazione
             .apply(match config.position {
                 TooltipPosition::Top => floem::style::Style::new().inset_bottom(100.0).inset_left(50.0),
                 TooltipPosition::Bottom => floem::style::Style::new().inset_top(100.0).inset_left(50.0),
                 TooltipPosition::Left => floem::style::Style::new().inset_right(100.0).inset_top(50.0),
                 TooltipPosition::Right => floem::style::Style::new().inset_left(100.0).inset_top(50.0),
             })
             // Inizialmente nascosto
             .display(floem::style::Display::None)
        });

    // Aggiungi il tooltip alla vista originale
    // TODO: Implement tooltip functionality when Floem 0.2 supports hover events
    // For now, return the view without tooltip overlay
    container(view)
        .style(|s| s.position(floem::style::Position::Relative))
}

/// Crea un tooltip informativo
#[allow(dead_code)]
pub fn info_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
) -> impl View {
    with_tooltip(
        theme_manager,
        view,
        text,
        Some(TooltipConfig {
            position: TooltipPosition::Top,
            delay_ms: 300,
            duration_ms: 0,
        }),
    )
}

/// Crea un tooltip di aiuto
#[allow(dead_code)]
pub fn help_tooltip<V: View + 'static>(
    theme_manager: Arc<ThemeManager>,
    view: V,
    text: &str,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let container_view = container(view)
        .style(move |s| {
            s.cursor(floem::style::CursorStyle::Default)
             .hover(|s| s.color(theme.colors.accent))
        });

    with_tooltip(
        theme_manager,
        container_view,
        text,
        Some(TooltipConfig {
            position: TooltipPosition::Bottom,
            delay_ms: 200,
            duration_ms: 0,
        }),
    )
}
