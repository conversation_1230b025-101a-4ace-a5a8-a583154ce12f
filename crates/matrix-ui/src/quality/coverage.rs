//! Code Coverage Module
//!
//! Questo modulo fornisce strumenti avanzati per il tracking
//! e l'analisi della code coverage.

use super::{QualityResult, QualityError};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Report di coverage dettagliato
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageReport {
    /// Timestamp del report
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Coverage complessivo
    pub overall_coverage: f64,
    /// Coverage per file
    pub file_coverage: HashMap<PathBuf, FileCoverage>,
    /// Coverage per funzione
    pub function_coverage: HashMap<String, FunctionCoverage>,
    /// Linee non coperte
    pub uncovered_lines: Vec<UncoveredLine>,
    /// Statistiche
    pub stats: CoverageStats,
}

/// Coverage di un singolo file
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileCoverage {
    /// Path del file
    pub path: PathBuf,
    /// Linee totali
    pub total_lines: usize,
    /// Linee coperte
    pub covered_lines: usize,
    /// Percentuale di coverage
    pub coverage_percentage: f64,
    /// Linee non coperte
    pub uncovered_line_numbers: Vec<usize>,
}

/// Coverage di una singola funzione
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionCoverage {
    /// Nome della funzione
    pub name: String,
    /// File contenente la funzione
    pub file: PathBuf,
    /// Linea di inizio
    pub start_line: usize,
    /// Linea di fine
    pub end_line: usize,
    /// Percentuale di coverage
    pub coverage_percentage: f64,
    /// È coperta da test?
    pub is_covered: bool,
}

/// Linea non coperta
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UncoveredLine {
    /// File
    pub file: PathBuf,
    /// Numero di linea
    pub line_number: usize,
    /// Contenuto della linea
    pub content: String,
    /// Tipo di statement
    pub statement_type: StatementType,
}

/// Tipo di statement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StatementType {
    /// Condizione if/else
    Conditional,
    /// Loop
    Loop,
    /// Funzione
    Function,
    /// Assegnazione
    Assignment,
    /// Return
    Return,
    /// Altro
    Other,
}

/// Statistiche di coverage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageStats {
    /// Numero totale di file
    pub total_files: usize,
    /// File completamente coperti
    pub fully_covered_files: usize,
    /// File parzialmente coperti
    pub partially_covered_files: usize,
    /// File non coperti
    pub uncovered_files: usize,
    /// Numero totale di funzioni
    pub total_functions: usize,
    /// Funzioni coperte
    pub covered_functions: usize,
    /// Numero totale di linee
    pub total_lines: usize,
    /// Linee coperte
    pub covered_lines: usize,
}

/// Analyzer per la code coverage
#[derive(Debug)]
pub struct CoverageAnalyzer {
    /// Directory del progetto
    project_root: PathBuf,
    /// Configurazione
    config: CoverageConfig,
}

/// Configurazione del coverage analyzer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageConfig {
    /// Soglia minima di coverage per file
    pub min_file_coverage: f64,
    /// Soglia minima di coverage per funzioni
    pub min_function_coverage: f64,
    /// Escludere file di test
    pub exclude_tests: bool,
    /// Pattern di file da escludere
    pub exclude_patterns: Vec<String>,
    /// Include solo pattern specifici
    pub include_patterns: Vec<String>,
}

impl Default for CoverageConfig {
    fn default() -> Self {
        Self {
            min_file_coverage: 80.0,
            min_function_coverage: 90.0,
            exclude_tests: true,
            exclude_patterns: vec![
                "*/tests/*".to_string(),
                "*/test_*".to_string(),
                "*_test.rs".to_string(),
                "*/benches/*".to_string(),
                "*/examples/*".to_string(),
            ],
            include_patterns: vec!["*.rs".to_string()],
        }
    }
}

impl CoverageAnalyzer {
    /// Crea un nuovo analyzer
    pub fn new(project_root: PathBuf, config: CoverageConfig) -> Self {
        Self {
            project_root,
            config,
        }
    }
    
    /// Crea un analyzer con configurazione di default
    pub fn with_default_config(project_root: PathBuf) -> Self {
        Self::new(project_root, CoverageConfig::default())
    }
    
    /// Genera un report di coverage dettagliato
    pub async fn generate_detailed_report(&self) -> QualityResult<CoverageReport> {
        // Esegue tarpaulin con output dettagliato
        let tarpaulin_output = self.run_tarpaulin().await?;
        
        // Parsa i risultati
        let coverage_data = self.parse_tarpaulin_output(&tarpaulin_output)?;
        
        // Analizza i file
        let file_coverage = self.analyze_file_coverage(&coverage_data).await?;
        
        // Analizza le funzioni
        let function_coverage = self.analyze_function_coverage(&coverage_data).await?;
        
        // Identifica linee non coperte
        let uncovered_lines = self.identify_uncovered_lines(&file_coverage).await?;
        
        // Calcola statistiche
        let stats = self.calculate_stats(&file_coverage, &function_coverage);
        
        // Calcola coverage complessivo
        let overall_coverage = if stats.total_lines > 0 {
            (stats.covered_lines as f64 / stats.total_lines as f64) * 100.0
        } else {
            0.0
        };
        
        Ok(CoverageReport {
            timestamp: chrono::Utc::now(),
            overall_coverage,
            file_coverage,
            function_coverage,
            uncovered_lines,
            stats,
        })
    }
    
    /// Esegue tarpaulin
    async fn run_tarpaulin(&self) -> QualityResult<String> {
        let output = std::process::Command::new("cargo")
            .args(&[
                "tarpaulin",
                "--output-format", "json",
                "--output-format", "lcov",
                "--skip-clean",
                "--exclude-files", &self.config.exclude_patterns.join(","),
            ])
            .current_dir(&self.project_root)
            .output()
            .map_err(|e| QualityError::MetricCollectionFailed(format!("Failed to run tarpaulin: {}", e)))?;
        
        if !output.status.success() {
            return Err(QualityError::MetricCollectionFailed(
                format!("Tarpaulin failed: {}", String::from_utf8_lossy(&output.stderr))
            ));
        }
        
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    }
    
    /// Parsa l'output di tarpaulin
    fn parse_tarpaulin_output(&self, output: &str) -> QualityResult<serde_json::Value> {
        // Cerca la parte JSON nell'output
        for line in output.lines() {
            if line.starts_with('{') && line.contains("coverage") {
                return serde_json::from_str(line)
                    .map_err(|e| QualityError::SerializationError(e));
            }
        }
        
        Err(QualityError::MetricCollectionFailed(
            "No valid JSON found in tarpaulin output".to_string()
        ))
    }
    
    /// Analizza il coverage per file
    async fn analyze_file_coverage(&self, _data: &serde_json::Value) -> QualityResult<HashMap<PathBuf, FileCoverage>> {
        let mut file_coverage = HashMap::new();
        
        // Implementazione semplificata - in un'implementazione reale
        // parserebbe i dati dettagliati di tarpaulin
        
        // Simula alcuni file con coverage
        let sample_files = vec![
            ("src/main.rs", 85.0, 100, 85),
            ("src/lib.rs", 92.0, 150, 138),
            ("src/components/mod.rs", 78.0, 80, 62),
            ("src/theme.rs", 95.0, 200, 190),
        ];
        
        for (path, coverage, total, covered) in sample_files {
            let file_path = PathBuf::from(path);
            let uncovered_count = total - covered;
            let uncovered_line_numbers: Vec<usize> = (1..=uncovered_count).collect();
            
            file_coverage.insert(file_path.clone(), FileCoverage {
                path: file_path,
                total_lines: total,
                covered_lines: covered,
                coverage_percentage: coverage,
                uncovered_line_numbers,
            });
        }
        
        Ok(file_coverage)
    }
    
    /// Analizza il coverage per funzioni
    async fn analyze_function_coverage(&self, _data: &serde_json::Value) -> QualityResult<HashMap<String, FunctionCoverage>> {
        let mut function_coverage = HashMap::new();
        
        // Implementazione semplificata
        let sample_functions = vec![
            ("main", "src/main.rs", 1, 20, 100.0),
            ("create_app", "src/lib.rs", 25, 50, 90.0),
            ("handle_event", "src/components/mod.rs", 15, 35, 75.0),
            ("load_theme", "src/theme.rs", 10, 30, 95.0),
        ];
        
        for (name, file, start, end, coverage) in sample_functions {
            function_coverage.insert(name.to_string(), FunctionCoverage {
                name: name.to_string(),
                file: PathBuf::from(file),
                start_line: start,
                end_line: end,
                coverage_percentage: coverage,
                is_covered: coverage >= self.config.min_function_coverage,
            });
        }
        
        Ok(function_coverage)
    }
    
    /// Identifica le linee non coperte
    async fn identify_uncovered_lines(&self, file_coverage: &HashMap<PathBuf, FileCoverage>) -> QualityResult<Vec<UncoveredLine>> {
        let mut uncovered_lines = Vec::new();
        
        for (file_path, coverage) in file_coverage {
            // Legge il contenuto del file
            if let Ok(content) = std::fs::read_to_string(file_path) {
                let lines: Vec<&str> = content.lines().collect();
                
                for &line_number in &coverage.uncovered_line_numbers {
                    if line_number > 0 && line_number <= lines.len() {
                        let line_content = lines[line_number - 1].trim();
                        
                        // Determina il tipo di statement
                        let statement_type = self.classify_statement(line_content);
                        
                        uncovered_lines.push(UncoveredLine {
                            file: file_path.clone(),
                            line_number,
                            content: line_content.to_string(),
                            statement_type,
                        });
                    }
                }
            }
        }
        
        Ok(uncovered_lines)
    }
    
    /// Classifica il tipo di statement
    fn classify_statement(&self, line: &str) -> StatementType {
        if line.contains("if ") || line.contains("else") || line.contains("match") {
            StatementType::Conditional
        } else if line.contains("for ") || line.contains("while ") || line.contains("loop") {
            StatementType::Loop
        } else if line.contains("fn ") {
            StatementType::Function
        } else if line.contains("return") {
            StatementType::Return
        } else if line.contains("=") && !line.contains("==") && !line.contains("!=") {
            StatementType::Assignment
        } else {
            StatementType::Other
        }
    }
    
    /// Calcola le statistiche
    fn calculate_stats(&self, file_coverage: &HashMap<PathBuf, FileCoverage>, function_coverage: &HashMap<String, FunctionCoverage>) -> CoverageStats {
        let total_files = file_coverage.len();
        let mut fully_covered_files = 0;
        let mut partially_covered_files = 0;
        let mut uncovered_files = 0;
        let mut total_lines = 0;
        let mut covered_lines = 0;
        
        for coverage in file_coverage.values() {
            total_lines += coverage.total_lines;
            covered_lines += coverage.covered_lines;
            
            if coverage.coverage_percentage >= 100.0 {
                fully_covered_files += 1;
            } else if coverage.coverage_percentage > 0.0 {
                partially_covered_files += 1;
            } else {
                uncovered_files += 1;
            }
        }
        
        let total_functions = function_coverage.len();
        let covered_functions = function_coverage.values()
            .filter(|f| f.is_covered)
            .count();
        
        CoverageStats {
            total_files,
            fully_covered_files,
            partially_covered_files,
            uncovered_files,
            total_functions,
            covered_functions,
            total_lines,
            covered_lines,
        }
    }
    
    /// Genera raccomandazioni per migliorare il coverage
    pub fn generate_coverage_recommendations(&self, report: &CoverageReport) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        // Raccomandazioni basate sul coverage complessivo
        if report.overall_coverage < self.config.min_file_coverage {
            recommendations.push(format!(
                "Il coverage complessivo ({:.1}%) è sotto la soglia minima ({:.1}%). Aggiungere test per le aree non coperte.",
                report.overall_coverage, self.config.min_file_coverage
            ));
        }
        
        // Raccomandazioni per file con basso coverage
        let low_coverage_files: Vec<_> = report.file_coverage.values()
            .filter(|f| f.coverage_percentage < self.config.min_file_coverage)
            .collect();
        
        if !low_coverage_files.is_empty() {
            recommendations.push(format!(
                "{} file(i) hanno coverage sotto la soglia. Priorità: {}",
                low_coverage_files.len(),
                low_coverage_files.iter()
                    .take(3)
                    .map(|f| format!("{} ({:.1}%)", f.path.display(), f.coverage_percentage))
                    .collect::<Vec<_>>()
                    .join(", ")
            ));
        }
        
        // Raccomandazioni per funzioni non coperte
        let uncovered_functions: Vec<_> = report.function_coverage.values()
            .filter(|f| !f.is_covered)
            .collect();
        
        if !uncovered_functions.is_empty() {
            recommendations.push(format!(
                "{} funzione(i) non sono coperte da test. Aggiungere unit test specifici.",
                uncovered_functions.len()
            ));
        }
        
        // Raccomandazioni per tipi di statement non coperti
        let conditional_uncovered = report.uncovered_lines.iter()
            .filter(|l| matches!(l.statement_type, StatementType::Conditional))
            .count();
        
        if conditional_uncovered > 0 {
            recommendations.push(format!(
                "{} condizioni non sono coperte. Aggiungere test per tutti i branch.",
                conditional_uncovered
            ));
        }
        
        if recommendations.is_empty() {
            recommendations.push("Eccellente! Il coverage è sopra tutte le soglie target. 🎉".to_string());
        }
        
        recommendations
    }
}
