//! Quality Dashboard Module
//!
//! Questo modulo fornisce un dashboard per visualizzare
//! le metriche di qualità in tempo reale.

use super::{QualityManager, QualityReport, QualityMetric, QualityLevel, QualityTrend};
use crate::theme::ThemeManager;
use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    style::Style,
    views::{
        container, label, v_stack, v_stack_from_iter, h_stack, scroll, empty,
        Decorators,
    },
    View,
    peniko::Color,
    text::Weight,
};
use std::sync::Arc;
use std::collections::HashMap;

/// Dashboard per le metriche di qualità
#[derive(Debug, Clone)]
pub struct QualityDashboard {
    /// Quality manager
    quality_manager: Arc<QualityManager>,
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    /// Report corrente
    current_report: RwSignal<Option<QualityReport>>,
    /// Metriche in tempo reale
    live_metrics: RwSignal<HashMap<String, QualityMetric>>,
}

impl QualityDashboard {
    /// Crea un nuovo dashboard
    pub fn new(
        quality_manager: Arc<QualityManager>,
        theme_manager: Arc<ThemeManager>,
    ) -> Self {
        let current_report = create_rw_signal(None);
        let live_metrics = create_rw_signal(HashMap::new());
        
        Self {
            quality_manager,
            theme_manager,
            current_report,
            live_metrics,
        }
    }
    
    /// Aggiorna il dashboard con un nuovo report
    pub fn update_report(&self, report: QualityReport) {
        self.current_report.set(Some(report.clone()));
        self.live_metrics.set(report.metrics);
    }
    
    /// Crea la vista del dashboard
    pub fn create_view(&self) -> impl View {
        let theme_manager = self.theme_manager.clone();

        container(
            v_stack((
                // Header del dashboard
                self.create_header(),

                // Metriche principali
                self.create_main_metrics(),

                // Grafici e trend
                self.create_charts_section(),

                // Raccomandazioni
                self.create_recommendations_section(),
            ))
            .style(|s| s.gap(16.0))
        )
        .style(move |s| {
            let theme = theme_manager.get_active_theme().unwrap_or_default();
            s.width_full()
                .height_full()
                .padding(16.0)
                .background(theme.colors.background)
        })
    }
    
    /// Crea l'header del dashboard
    fn create_header(&self) -> impl View {
        let theme_manager = self.theme_manager.clone();
        let current_report = self.current_report;

        container(
            h_stack((
                // Titolo
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(|| "Quality Dashboard".to_string())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.xl)
                                .color(theme.colors.text)
                                .font_weight(Weight::BOLD)
                        })
                },

                // Score complessivo
                {
                    let theme_manager_clone = theme_manager.clone();
                    let theme_manager_clone2 = theme_manager.clone();
                    container(
                        label(move || {
                            if let Some(report) = current_report.get() {
                                format!("Overall Score: {:.1}%", report.overall_score)
                            } else {
                                "No data".to_string()
                            }
                        })
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.lg)
                                .color(theme.colors.text)
                        })
                    )
                    .style(move |s| {
                        let theme = theme_manager_clone2.get_active_theme().unwrap_or_default();
                        s.padding(8.0)
                            .border_radius(4.0)
                            .background(theme.colors.background_secondary)
                    })
                },
                
                // Trend indicator
                {
                    let theme_manager_clone = theme_manager.clone();
                    let theme_manager_clone2 = theme_manager.clone();
                    container(
                        label(move || {
                            if let Some(report) = current_report.get() {
                                format!("{} {}", report.trend.icon(), match report.trend {
                                    QualityTrend::Improving => "Improving",
                                    QualityTrend::Stable => "Stable",
                                    QualityTrend::Declining => "Declining",
                                })
                            } else {
                                "".to_string()
                            }
                        })
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.md)
                                .color(theme.colors.text)
                        })
                    )
                    .style(move |s| {
                        let theme = theme_manager_clone2.get_active_theme().unwrap_or_default();
                        s.padding(8.0)
                            .border_radius(4.0)
                            .background(theme.colors.background_secondary)
                    })
                },
            ))
            .style(|s| s.justify_between().items_center())
        )
        .style(move |s| {
            let theme = theme_manager.clone().get_active_theme().unwrap_or_default();
            s.width_full()
                .padding(16.0)
                .border_radius(8.0)
                .background(theme.colors.background_secondary)
        })
    }
    
    /// Crea la sezione delle metriche principali
    fn create_main_metrics(&self) -> impl View {
        let theme_manager = self.theme_manager.clone();
        let live_metrics = self.live_metrics;

        container(
            v_stack((
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(|| "Key Metrics".to_string())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.lg)
                                .color(theme.colors.text)
                                .font_weight(Weight::BOLD)
                        })
                },
                
                // Grid di metriche
                container(
                    h_stack((
                        self.create_metric_card("code_coverage", "Code Coverage"),
                        self.create_metric_card("cyclomatic_complexity", "Complexity"),
                        self.create_metric_card("code_duplication", "Duplication"),
                        self.create_metric_card("performance_score", "Performance"),
                    ))
                    .style(|s| s.gap(16.0))
                )
                .style(|s| s.width_full()),
            ))
            .style(|s| s.gap(12.0))
        )
        .style(move |s| {
            let theme = theme_manager.clone().get_active_theme().unwrap_or_default();
            s.width_full()
                .padding(16.0)
                .border_radius(8.0)
                .background(theme.colors.background_secondary)
        })
    }
    
    /// Crea una card per una metrica
    fn create_metric_card(&self, metric_name: &'static str, display_name: &'static str) -> impl View {
        let theme_manager = self.theme_manager.clone();
        let live_metrics = self.live_metrics;
        
        container(
            v_stack((
                // Nome della metrica
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(move || display_name.to_string())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.sm)
                                .color(theme.colors.text_secondary)
                        })
                },

                // Valore della metrica
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(move || {
                        if let Some(metric) = live_metrics.get().get(metric_name) {
                            format!("{:.1}{}", metric.value, metric.unit)
                        } else {
                            "N/A".to_string()
                        }
                    })
                    .style(move |s| {
                        let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                        s.font_size(theme.font_sizes.xl)
                            .color(theme.colors.text)
                            .font_weight(Weight::BOLD)
                    })
                },
                
                // Target e percentuale
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(move || {
                        if let Some(metric) = live_metrics.get().get(metric_name) {
                            format!("Target: {:.1}{} ({:.1}%)",
                                   metric.target, metric.unit, metric.percentage())
                        } else {
                            "".to_string()
                        }
                    })
                    .style(move |s| {
                        let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                        s.font_size(theme.font_sizes.xs)
                            .color(theme.colors.text_tertiary)
                    })
                },

                // Indicatore di livello
                {
                    let theme_manager_clone = theme_manager.clone();
                    container(empty())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.width_full()
                                .height(4.0)
                                .background(theme.colors.border)
                                .border_radius(2.0)
                        })
                },
            ))
            .style(|s| s.gap(8.0))
        )
        .style(move |s| {
            let theme = theme_manager.get_active_theme().unwrap_or_default();
            s.flex_grow(1.0)
                .padding(16.0)
                .border_radius(8.0)
                .background(theme.colors.background_tertiary)
                .border(1.0)
                .border_color(theme.colors.border)
        })
    }
    
    /// Crea la sezione dei grafici
    fn create_charts_section(&self) -> impl View {
        let theme_manager = self.theme_manager.clone();

        container(
            v_stack((
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(|| "Trends & Charts".to_string())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.lg)
                                .color(theme.colors.text)
                                .font_weight(Weight::BOLD)
                        })
                },

                // Placeholder per grafici
                {
                    let theme_manager_clone = theme_manager.clone();
                    container(
                        {
                            let theme_manager_clone2 = theme_manager_clone.clone();
                            label(|| "📊 Charts will be implemented here".to_string())
                                .style(move |s| {
                                    let theme = theme_manager_clone2.get_active_theme().unwrap_or_default();
                                    s.font_size(theme.font_sizes.md)
                                        .color(theme.colors.text_secondary)
                                })
                        }
                    )
                    .style(move |s| {
                        let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                        s.width_full()
                            .height(200.0)
                            .justify_center()
                            .items_center()
                            .border(2.0)
                            .border_color(theme.colors.border)
                            .border_radius(8.0)
                            .background(theme.colors.background_tertiary)
                    })
                },
            ))
            .style(|s| s.gap(12.0))
        )
        .style(move |s| {
            let theme = theme_manager.get_active_theme().unwrap_or_default();
            s.width_full()
                .padding(16.0)
                .border_radius(8.0)
                .background(theme.colors.background_secondary)
        })
    }
    
    /// Crea la sezione delle raccomandazioni
    fn create_recommendations_section(&self) -> impl View {
        let theme_manager = self.theme_manager.clone();
        let current_report = self.current_report;

        container(
            v_stack((
                {
                    let theme_manager_clone = theme_manager.clone();
                    label(|| "Recommendations".to_string())
                        .style(move |s| {
                            let theme = theme_manager_clone.get_active_theme().unwrap_or_default();
                            s.font_size(theme.font_sizes.lg)
                                .color(theme.colors.text)
                                .font_weight(Weight::BOLD)
                        })
                },
                
                // Lista raccomandazioni
                scroll({
                    let recommendations = if let Some(report) = current_report.get() {
                        report.recommendations.into_iter().enumerate().map(|(i, rec)| {
                            container(
                                h_stack((
                                    label(move || format!("{}.", i + 1))
                                        .style(|s| s.font_weight(Weight::BOLD)),

                                    label(move || rec.clone()),
                                ))
                                .style(|s| s.gap(8.0).items_start())
                            )
                            .style(|s| {
                                s.width_full()
                                    .padding(12.0)
                                    .border_radius(6.0)
                                    .border(1.0)
                            })
                        }).collect::<Vec<_>>()
                    } else {
                        vec![
                            container(
                                label(|| "No recommendations available".to_string())
                            )
                            .style(|s| {
                                s.width_full()
                                    .padding(12.0)
                                    .justify_center()
                                    .items_center()
                            })
                        ]
                    };

                    v_stack_from_iter(recommendations)
                        .style(|s| s.gap(8.0))
                })
                .style(|s| s.width_full().max_height(200.0)),
            ))
            .style(|s| s.gap(12.0))
        )
        .style(move |s| {
            let theme = theme_manager.get_active_theme().unwrap_or_default();
            s.width_full()
                .padding(16.0)
                .border_radius(8.0)
                .background(theme.colors.background_secondary)
        })
    }
    
    /// Avvia l'aggiornamento automatico del dashboard
    pub async fn start_auto_update(&self, interval_seconds: u64) {
        let quality_manager = Arc::clone(&self.quality_manager);
        let dashboard = self.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(interval_seconds));
            
            loop {
                interval.tick().await;
                
                if let Ok(report) = quality_manager.generate_report() {
                    dashboard.update_report(report);
                }
            }
        });
    }
}
