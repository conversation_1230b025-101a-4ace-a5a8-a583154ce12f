//! Metrics Collection Module
//!
//! Questo modulo fornisce strumenti per la raccolta automatica
//! di metriche di qualità del codice.

use super::{QualityMetric, QualityResult, QualityError};
use std::process::Command;
use std::path::Path;
use serde_json::Value;
use std::collections::HashMap;

/// Collector per metriche di qualità
#[derive(Debug)]
pub struct MetricsCollector {
    /// Directory del progetto
    project_root: std::path::PathBuf,
}

impl MetricsCollector {
    /// Crea un nuovo collector
    pub fn new<P: AsRef<Path>>(project_root: P) -> Self {
        Self {
            project_root: project_root.as_ref().to_path_buf(),
        }
    }
    
    /// Raccoglie tutte le metriche disponibili
    pub async fn collect_all_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Raccoglie metriche di coverage
        if let Ok(coverage) = self.collect_coverage_metrics().await {
            metrics.extend(coverage);
        }
        
        // Raccoglie metriche di complessità
        if let Ok(complexity) = self.collect_complexity_metrics().await {
            metrics.extend(complexity);
        }
        
        // Raccoglie metriche di duplicazione
        if let Ok(duplication) = self.collect_duplication_metrics().await {
            metrics.extend(duplication);
        }
        
        // Raccoglie metriche di performance
        if let Ok(performance) = self.collect_performance_metrics().await {
            metrics.extend(performance);
        }
        
        // Raccoglie metriche di sicurezza
        if let Ok(security) = self.collect_security_metrics().await {
            metrics.extend(security);
        }
        
        Ok(metrics)
    }
    
    /// Raccoglie metriche di code coverage
    pub async fn collect_coverage_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Esegue tarpaulin per il coverage
        let output = Command::new("cargo")
            .args(&["tarpaulin", "--output-format", "json", "--skip-clean"])
            .current_dir(&self.project_root)
            .output()
            .map_err(|e| QualityError::MetricCollectionFailed(format!("Failed to run tarpaulin: {}", e)))?;
        
        if output.status.success() {
            let coverage_data: Value = serde_json::from_slice(&output.stdout)
                .map_err(|e| QualityError::MetricCollectionFailed(format!("Failed to parse coverage data: {}", e)))?;
            
            if let Some(coverage_percent) = coverage_data["coverage"].as_f64() {
                let metric = QualityMetric::new(
                    "code_coverage".to_string(),
                    coverage_percent,
                    80.0, // Target 80%
                    "%".to_string(),
                    "Percentuale di codice coperto dai test".to_string(),
                );
                metrics.insert("code_coverage".to_string(), metric);
            }
            
            // Metriche per linee coperte
            if let Some(lines_covered) = coverage_data["lines_covered"].as_u64() {
                if let Some(lines_total) = coverage_data["lines_total"].as_u64() {
                    let metric = QualityMetric::new(
                        "lines_covered".to_string(),
                        lines_covered as f64,
                        lines_total as f64,
                        "lines".to_string(),
                        "Numero di linee di codice coperte dai test".to_string(),
                    );
                    metrics.insert("lines_covered".to_string(), metric);
                }
            }
        }
        
        Ok(metrics)
    }
    
    /// Raccoglie metriche di complessità ciclomatica
    pub async fn collect_complexity_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Usa tokei per contare le linee di codice
        let output = Command::new("tokei")
            .args(&["--output", "json"])
            .current_dir(&self.project_root)
            .output();
        
        if let Ok(output) = output {
            if output.status.success() {
                let tokei_data: Value = serde_json::from_slice(&output.stdout)
                    .map_err(|e| QualityError::MetricCollectionFailed(format!("Failed to parse tokei data: {}", e)))?;
                
                if let Some(rust_data) = tokei_data["Rust"].as_object() {
                    if let Some(code_lines) = rust_data["code"].as_u64() {
                        let metric = QualityMetric::new(
                            "lines_of_code".to_string(),
                            code_lines as f64,
                            10000.0, // Target massimo 10k linee
                            "lines".to_string(),
                            "Numero totale di linee di codice".to_string(),
                        );
                        metrics.insert("lines_of_code".to_string(), metric);
                    }
                }
            }
        }
        
        // Simula complessità ciclomatica (in un'implementazione reale useresti uno strumento specifico)
        let estimated_complexity = self.estimate_cyclomatic_complexity().await?;
        let metric = QualityMetric::new(
            "cyclomatic_complexity".to_string(),
            estimated_complexity,
            10.0, // Target massimo 10
            "score".to_string(),
            "Complessità ciclomatica media del codice".to_string(),
        );
        metrics.insert("cyclomatic_complexity".to_string(), metric);
        
        Ok(metrics)
    }
    
    /// Raccoglie metriche di duplicazione del codice
    pub async fn collect_duplication_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Simula analisi di duplicazione (in un'implementazione reale useresti uno strumento come jscpd)
        let estimated_duplication = self.estimate_code_duplication().await?;
        let metric = QualityMetric::new(
            "code_duplication".to_string(),
            estimated_duplication,
            5.0, // Target massimo 5%
            "%".to_string(),
            "Percentuale di codice duplicato".to_string(),
        );
        metrics.insert("code_duplication".to_string(), metric);
        
        Ok(metrics)
    }
    
    /// Raccoglie metriche di performance
    pub async fn collect_performance_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Esegue benchmark per le performance
        let output = Command::new("cargo")
            .args(&["bench", "--", "--output-format", "json"])
            .current_dir(&self.project_root)
            .output();
        
        if let Ok(output) = output {
            if output.status.success() {
                // Simula parsing dei risultati benchmark
                let performance_score = self.calculate_performance_score(&output.stdout).await?;
                let metric = QualityMetric::new(
                    "performance_score".to_string(),
                    performance_score,
                    85.0, // Target 85%
                    "score".to_string(),
                    "Score di performance basato sui benchmark".to_string(),
                );
                metrics.insert("performance_score".to_string(), metric);
            }
        }
        
        Ok(metrics)
    }
    
    /// Raccoglie metriche di sicurezza
    pub async fn collect_security_metrics(&self) -> QualityResult<HashMap<String, QualityMetric>> {
        let mut metrics = HashMap::new();
        
        // Esegue cargo audit per vulnerabilità
        let output = Command::new("cargo")
            .args(&["audit", "--format", "json"])
            .current_dir(&self.project_root)
            .output();
        
        if let Ok(output) = output {
            let vulnerabilities_count = if output.status.success() {
                // Nessuna vulnerabilità trovata
                0.0
            } else {
                // Conta le vulnerabilità dal JSON output
                self.count_vulnerabilities(&output.stdout).await.unwrap_or(0.0)
            };
            
            let metric = QualityMetric::new(
                "security_vulnerabilities".to_string(),
                vulnerabilities_count,
                0.0, // Target 0 vulnerabilità
                "count".to_string(),
                "Numero di vulnerabilità di sicurezza rilevate".to_string(),
            );
            metrics.insert("security_vulnerabilities".to_string(), metric);
        }
        
        Ok(metrics)
    }
    
    /// Stima la complessità ciclomatica
    async fn estimate_cyclomatic_complexity(&self) -> QualityResult<f64> {
        // Implementazione semplificata: conta il numero di branch points
        let mut complexity = 1.0; // Base complexity
        
        // Conta file .rs
        let rust_files = self.count_rust_files().await?;
        
        // Stima basata sul numero di file (molto semplificata)
        complexity += rust_files as f64 * 0.1;
        
        Ok(complexity.min(20.0)) // Cap a 20
    }
    
    /// Stima la duplicazione del codice
    async fn estimate_code_duplication(&self) -> QualityResult<f64> {
        // Implementazione semplificata
        let rust_files = self.count_rust_files().await?;
        
        // Stima basata sul numero di file
        let duplication = if rust_files > 50 {
            8.0 // Progetti grandi tendono ad avere più duplicazione
        } else if rust_files > 20 {
            5.0
        } else {
            2.0
        };
        
        Ok(duplication)
    }
    
    /// Calcola il performance score
    async fn calculate_performance_score(&self, _benchmark_output: &[u8]) -> QualityResult<f64> {
        // Implementazione semplificata
        // In un'implementazione reale, parserebbe i risultati dei benchmark
        Ok(87.5) // Score simulato
    }
    
    /// Conta le vulnerabilità
    async fn count_vulnerabilities(&self, _audit_output: &[u8]) -> QualityResult<f64> {
        // Implementazione semplificata
        // In un'implementazione reale, parserebbe l'output di cargo audit
        Ok(0.0) // Nessuna vulnerabilità simulata
    }
    
    /// Conta i file Rust nel progetto
    async fn count_rust_files(&self) -> QualityResult<usize> {
        let mut count = 0;
        
        fn count_rust_files_recursive(dir: &Path, count: &mut usize) -> std::io::Result<()> {
            if dir.is_dir() {
                for entry in std::fs::read_dir(dir)? {
                    let entry = entry?;
                    let path = entry.path();
                    
                    if path.is_dir() && !path.file_name().unwrap().to_str().unwrap().starts_with('.') {
                        count_rust_files_recursive(&path, count)?;
                    } else if path.extension().and_then(|s| s.to_str()) == Some("rs") {
                        *count += 1;
                    }
                }
            }
            Ok(())
        }
        
        count_rust_files_recursive(&self.project_root, &mut count)
            .map_err(|e| QualityError::MetricCollectionFailed(format!("Failed to count Rust files: {}", e)))?;
        
        Ok(count)
    }
}

/// Scheduler per la raccolta automatica di metriche
#[derive(Debug)]
pub struct MetricsScheduler {
    collector: MetricsCollector,
    interval: std::time::Duration,
}

impl MetricsScheduler {
    /// Crea un nuovo scheduler
    pub fn new<P: AsRef<Path>>(project_root: P, interval_seconds: u64) -> Self {
        Self {
            collector: MetricsCollector::new(project_root),
            interval: std::time::Duration::from_secs(interval_seconds),
        }
    }
    
    /// Avvia la raccolta automatica di metriche
    pub async fn start_collection<F>(&self, mut callback: F) -> QualityResult<()>
    where
        F: FnMut(HashMap<String, QualityMetric>) + Send + 'static,
    {
        let mut interval = tokio::time::interval(self.interval);
        
        loop {
            interval.tick().await;
            
            match self.collector.collect_all_metrics().await {
                Ok(metrics) => {
                    callback(metrics);
                }
                Err(e) => {
                    eprintln!("Failed to collect metrics: {}", e);
                }
            }
        }
    }
}
