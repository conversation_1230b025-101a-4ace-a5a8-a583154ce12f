//! Quality Assurance Module
//!
//! Questo modulo fornisce strumenti per il monitoraggio della qualità del codice,
//! metriche di performance e dashboard di quality assurance.

pub mod metrics;
pub mod dashboard;
pub mod coverage;
pub mod performance;
pub mod quality_gates;

// Re-export principali strutture
pub use metrics::{MetricsCollector, MetricsScheduler};
pub use dashboard::QualityDashboard;
pub use coverage::{CoverageAnalyzer, CoverageReport};
pub use performance::{PerformanceAnalyzer, PerformanceReport};
pub use quality_gates::{QualityGate, QualityGateRunner};

use std::sync::Arc;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

/// Errori del sistema di quality assurance
#[derive(Debug, thiserror::Error)]
pub enum QualityError {
    #[error("Metric collection failed: {0}")]
    MetricCollectionFailed(String),
    
    #[error("Quality gate failed: {0}")]
    QualityGateFailed(String),
    
    #[error("Performance threshold exceeded: {0}")]
    PerformanceThresholdExceeded(String),
    
    #[error("Coverage below minimum: expected {expected}%, got {actual}%")]
    CoverageBelowMinimum { expected: f64, actual: f64 },
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}

/// Risultato delle operazioni di quality assurance
pub type QualityResult<T> = Result<T, QualityError>;

/// Livello di qualità
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum QualityLevel {
    /// Eccellente (90-100%)
    Excellent,
    /// Buono (80-89%)
    Good,
    /// Accettabile (70-79%)
    Acceptable,
    /// Scarso (60-69%)
    Poor,
    /// Critico (<60%)
    Critical,
}

impl QualityLevel {
    /// Determina il livello di qualità da una percentuale
    pub fn from_percentage(percentage: f64) -> Self {
        match percentage {
            p if p >= 90.0 => QualityLevel::Excellent,
            p if p >= 80.0 => QualityLevel::Good,
            p if p >= 70.0 => QualityLevel::Acceptable,
            p if p >= 60.0 => QualityLevel::Poor,
            _ => QualityLevel::Critical,
        }
    }
    
    /// Converte il livello in una percentuale minima
    pub fn to_percentage(&self) -> f64 {
        match self {
            QualityLevel::Excellent => 90.0,
            QualityLevel::Good => 80.0,
            QualityLevel::Acceptable => 70.0,
            QualityLevel::Poor => 60.0,
            QualityLevel::Critical => 0.0,
        }
    }
    
    /// Restituisce il colore associato al livello
    pub fn color(&self) -> &'static str {
        match self {
            QualityLevel::Excellent => "#4CAF50", // Verde
            QualityLevel::Good => "#8BC34A",      // Verde chiaro
            QualityLevel::Acceptable => "#FFC107", // Giallo
            QualityLevel::Poor => "#FF9800",       // Arancione
            QualityLevel::Critical => "#F44336",   // Rosso
        }
    }
}

/// Metrica di qualità
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityMetric {
    /// Nome della metrica
    pub name: String,
    /// Valore attuale
    pub value: f64,
    /// Valore target
    pub target: f64,
    /// Unità di misura
    pub unit: String,
    /// Timestamp della misurazione
    pub timestamp: DateTime<Utc>,
    /// Livello di qualità
    pub level: QualityLevel,
    /// Descrizione della metrica
    pub description: String,
}

impl QualityMetric {
    /// Crea una nuova metrica
    pub fn new(
        name: String,
        value: f64,
        target: f64,
        unit: String,
        description: String,
    ) -> Self {
        let percentage = if target > 0.0 { (value / target) * 100.0 } else { 0.0 };
        let level = QualityLevel::from_percentage(percentage);
        
        Self {
            name,
            value,
            target,
            unit,
            timestamp: Utc::now(),
            level,
            description,
        }
    }
    
    /// Verifica se la metrica supera il target
    pub fn meets_target(&self) -> bool {
        self.value >= self.target
    }
    
    /// Calcola la percentuale rispetto al target
    pub fn percentage(&self) -> f64 {
        if self.target > 0.0 {
            (self.value / self.target) * 100.0
        } else {
            0.0
        }
    }
}

/// Report di qualità
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityReport {
    /// Timestamp del report
    pub timestamp: DateTime<Utc>,
    /// Metriche di qualità
    pub metrics: HashMap<String, QualityMetric>,
    /// Livello di qualità complessivo
    pub overall_level: QualityLevel,
    /// Score complessivo (0-100)
    pub overall_score: f64,
    /// Raccomandazioni
    pub recommendations: Vec<String>,
    /// Trend rispetto al report precedente
    pub trend: QualityTrend,
}

/// Trend di qualità
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum QualityTrend {
    /// Miglioramento
    Improving,
    /// Stabile
    Stable,
    /// Peggioramento
    Declining,
}

impl QualityTrend {
    /// Determina il trend da due score
    pub fn from_scores(current: f64, previous: f64) -> Self {
        let diff = current - previous;
        if diff > 1.0 {
            QualityTrend::Improving
        } else if diff < -1.0 {
            QualityTrend::Declining
        } else {
            QualityTrend::Stable
        }
    }
    
    /// Restituisce l'icona del trend
    pub fn icon(&self) -> &'static str {
        match self {
            QualityTrend::Improving => "↗️",
            QualityTrend::Stable => "➡️",
            QualityTrend::Declining => "↘️",
        }
    }
    
    /// Restituisce il colore del trend
    pub fn color(&self) -> &'static str {
        match self {
            QualityTrend::Improving => "#4CAF50",
            QualityTrend::Stable => "#2196F3",
            QualityTrend::Declining => "#F44336",
        }
    }
}

/// Manager per la quality assurance
#[derive(Debug)]
pub struct QualityManager {
    /// Metriche correnti
    metrics: Arc<std::sync::RwLock<HashMap<String, QualityMetric>>>,
    /// Storico dei report
    history: Arc<std::sync::RwLock<Vec<QualityReport>>>,
    /// Configurazione
    config: QualityConfig,
}

/// Configurazione del quality manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityConfig {
    /// Soglia minima di coverage
    pub min_coverage: f64,
    /// Soglia massima di complessità ciclomatica
    pub max_complexity: f64,
    /// Soglia massima di duplicazione
    pub max_duplication: f64,
    /// Soglia minima di performance
    pub min_performance_score: f64,
    /// Intervallo di raccolta metriche (secondi)
    pub collection_interval: u64,
    /// Numero massimo di report storici da mantenere
    pub max_history_size: usize,
}

impl Default for QualityConfig {
    fn default() -> Self {
        Self {
            min_coverage: 80.0,
            max_complexity: 10.0,
            max_duplication: 5.0,
            min_performance_score: 85.0,
            collection_interval: 300, // 5 minuti
            max_history_size: 100,
        }
    }
}

impl QualityManager {
    /// Crea un nuovo quality manager
    pub fn new(config: QualityConfig) -> Self {
        Self {
            metrics: Arc::new(std::sync::RwLock::new(HashMap::new())),
            history: Arc::new(std::sync::RwLock::new(Vec::new())),
            config,
        }
    }
    
    /// Crea un quality manager con configurazione di default
    pub fn with_default_config() -> Self {
        Self::new(QualityConfig::default())
    }
    
    /// Aggiunge una metrica
    pub fn add_metric(&self, metric: QualityMetric) -> QualityResult<()> {
        let mut metrics = self.metrics.write().unwrap();
        metrics.insert(metric.name.clone(), metric);
        Ok(())
    }
    
    /// Ottiene una metrica per nome
    pub fn get_metric(&self, name: &str) -> Option<QualityMetric> {
        let metrics = self.metrics.read().unwrap();
        metrics.get(name).cloned()
    }
    
    /// Ottiene tutte le metriche
    pub fn get_all_metrics(&self) -> HashMap<String, QualityMetric> {
        let metrics = self.metrics.read().unwrap();
        metrics.clone()
    }
    
    /// Genera un report di qualità
    pub fn generate_report(&self) -> QualityResult<QualityReport> {
        let metrics = self.get_all_metrics();
        
        // Calcola score complessivo
        let overall_score = self.calculate_overall_score(&metrics);
        let overall_level = QualityLevel::from_percentage(overall_score);
        
        // Determina trend
        let trend = {
            let history = self.history.read().unwrap();
            if let Some(last_report) = history.last() {
                QualityTrend::from_scores(overall_score, last_report.overall_score)
            } else {
                QualityTrend::Stable
            }
        };
        
        // Genera raccomandazioni
        let recommendations = self.generate_recommendations(&metrics);
        
        let report = QualityReport {
            timestamp: Utc::now(),
            metrics,
            overall_level,
            overall_score,
            recommendations,
            trend,
        };
        
        // Salva nel history
        {
            let mut history = self.history.write().unwrap();
            history.push(report.clone());
            
            // Mantieni solo gli ultimi N report
            if history.len() > self.config.max_history_size {
                history.remove(0);
            }
        }
        
        Ok(report)
    }
    
    /// Calcola lo score complessivo
    fn calculate_overall_score(&self, metrics: &HashMap<String, QualityMetric>) -> f64 {
        if metrics.is_empty() {
            return 0.0;
        }
        
        let total: f64 = metrics.values().map(|m| m.percentage()).sum();
        total / metrics.len() as f64
    }
    
    /// Genera raccomandazioni basate sulle metriche
    fn generate_recommendations(&self, metrics: &HashMap<String, QualityMetric>) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        for metric in metrics.values() {
            if !metric.meets_target() {
                match metric.name.as_str() {
                    "code_coverage" => {
                        recommendations.push(format!(
                            "Aumentare la copertura del codice dal {:.1}% al {:.1}%",
                            metric.percentage(),
                            metric.target
                        ));
                    }
                    "cyclomatic_complexity" => {
                        recommendations.push(
                            "Ridurre la complessità ciclomatica refactoring le funzioni più complesse".to_string()
                        );
                    }
                    "code_duplication" => {
                        recommendations.push(
                            "Eliminare la duplicazione del codice estraendo funzioni comuni".to_string()
                        );
                    }
                    "performance_score" => {
                        recommendations.push(
                            "Ottimizzare le performance identificando e risolvendo i bottleneck".to_string()
                        );
                    }
                    _ => {
                        recommendations.push(format!(
                            "Migliorare la metrica '{}' dal {:.1}% al target del {:.1}%",
                            metric.name,
                            metric.percentage(),
                            metric.target
                        ));
                    }
                }
            }
        }
        
        if recommendations.is_empty() {
            recommendations.push("Tutte le metriche di qualità sono nel target! 🎉".to_string());
        }
        
        recommendations
    }
    
    /// Ottiene lo storico dei report
    pub fn get_history(&self) -> Vec<QualityReport> {
        let history = self.history.read().unwrap();
        history.clone()
    }
    
    /// Verifica se tutte le quality gate sono superate
    pub fn check_quality_gates(&self) -> QualityResult<bool> {
        let metrics = self.get_all_metrics();
        
        for metric in metrics.values() {
            if !metric.meets_target() {
                return Err(QualityError::QualityGateFailed(format!(
                    "Metric '{}' failed: {:.2} < {:.2}",
                    metric.name, metric.value, metric.target
                )));
            }
        }
        
        Ok(true)
    }
}
